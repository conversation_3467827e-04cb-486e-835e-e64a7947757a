<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏁 Racing Car Championship</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
            font-family: 'Arial Black', Arial, sans-serif;
            color: white;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        canvas {
            display: block;
        }

        .racing-hud {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .race-dashboard {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 3px solid #ff6b6b;
            min-width: 300px;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        .race-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b6b;
            text-align: center;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .race-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .race-stat {
            background: rgba(255, 107, 107, 0.2);
            padding: 0.8rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 107, 107, 0.5);
            text-align: center;
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
            margin-bottom: 0.3rem;
        }

        .stat-value {
            font-size: 1.3rem;
            font-weight: bold;
            color: #ff6b6b;
        }

        .lap-progress {
            margin-top: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ff8e53);
            border-radius: 6px;
            transition: width 0.3s ease;
        }

        .leaderboard {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 3px solid #4ecdc4;
            min-width: 250px;
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
        }

        .leaderboard-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #4ecdc4;
            text-align: center;
            margin-bottom: 1rem;
        }

        .racer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: rgba(78, 205, 196, 0.1);
            border-radius: 5px;
            border-left: 3px solid #4ecdc4;
        }

        .racer-position {
            font-weight: bold;
            color: #4ecdc4;
            margin-right: 0.5rem;
        }

        .racer-name {
            flex: 1;
        }

        .racer-time {
            font-family: monospace;
            color: #45b7d1;
        }

        .speed-gauge {
            position: absolute;
            bottom: 30px;
            right: 30px;
            width: 180px;
            height: 180px;
            background: radial-gradient(circle, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.6) 100%);
            border: 4px solid #45b7d1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            box-shadow: 0 0 30px rgba(69, 183, 209, 0.6);
        }

        .speed-needle {
            position: absolute;
            width: 3px;
            height: 70px;
            background: #ff6b6b;
            transform-origin: bottom center;
            transition: transform 0.2s ease;
            border-radius: 2px;
            box-shadow: 0 0 10px #ff6b6b;
        }

        .speed-display {
            font-size: 2.5rem;
            font-weight: bold;
            color: #45b7d1;
            text-shadow: 0 0 10px #45b7d1;
            z-index: 1;
        }

        .speed-unit {
            font-size: 1rem;
            color: #ccc;
            z-index: 1;
        }

        .race-timer {
            position: absolute;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 3px solid #45b7d1;
            text-align: center;
            box-shadow: 0 0 20px rgba(69, 183, 209, 0.5);
        }

        .timer-label {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }

        .timer-value {
            font-size: 2rem;
            font-weight: bold;
            color: #45b7d1;
            font-family: monospace;
        }

        .race-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem 2rem;
            border-radius: 15px;
            border: 3px solid #ff6b6b;
            text-align: center;
            font-size: 0.9rem;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        .race-notification {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            padding: 2rem 3rem;
            border-radius: 20px;
            border: 3px solid #ff6b6b;
            text-align: center;
            font-size: 1.5rem;
            z-index: 200;
            animation: raceNotification 0.8s ease-out;
            box-shadow: 0 0 30px rgba(255, 107, 107, 0.8);
        }

        @keyframes raceNotification {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .countdown {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 8rem;
            font-weight: bold;
            color: #ff6b6b;
            text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8);
            z-index: 300;
            animation: countdownPulse 1s ease-in-out;
        }

        @keyframes countdownPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        .loading-race {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
        }

        .loading-title {
            font-size: 4rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: raceGradient 3s ease-in-out infinite;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        @keyframes raceGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .loading-progress {
            width: 400px;
            height: 10px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            overflow: hidden;
            margin: 2rem 0;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 5px;
            animation: raceLoading 3s ease-in-out;
        }

        @keyframes raceLoading {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .race-features {
            text-align: left;
            margin-top: 2rem;
            opacity: 0.9;
        }

        .race-feature {
            margin: 0.8rem 0;
            padding-left: 1.5rem;
            font-size: 1.1rem;
        }

        .minimap {
            position: absolute;
            bottom: 30px;
            left: 30px;
            width: 150px;
            height: 150px;
            background: rgba(0, 0, 0, 0.9);
            border: 3px solid #4ecdc4;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
        }

        .boost-indicator {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #ff6b6b;
            writing-mode: vertical-rl;
            text-orientation: mixed;
        }

        .boost-bar {
            width: 20px;
            height: 100px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .boost-fill {
            width: 100%;
            background: linear-gradient(0deg, #ff6b6b, #ff8e53);
            border-radius: 10px;
            transition: height 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading" class="loading-race">
            <div class="loading-title">🏁 RACING CHAMPIONSHIP</div>
            <p style="font-size: 1.3rem; margin-bottom: 1rem;">Preparing Race Track...</p>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
            <div class="race-features">
                <div class="race-feature">🏎️ High-Speed Racing Physics</div>
                <div class="race-feature">🏁 Championship Mode</div>
                <div class="race-feature">🎯 Lap-Based Racing</div>
                <div class="race-feature">🏆 AI Competitors</div>
                <div class="race-feature">💨 Boost System</div>
                <div class="race-feature">📊 Real-time Leaderboard</div>
            </div>
        </div>

        <div class="racing-hud" id="hud" style="display: none;">
            <!-- Race Dashboard -->
            <div class="race-dashboard">
                <div class="race-title">🏁 RACE CHAMPIONSHIP</div>
                <div class="race-stats">
                    <div class="race-stat">
                        <div class="stat-label">Current Lap</div>
                        <div class="stat-value" id="currentLap">1/5</div>
                    </div>
                    <div class="race-stat">
                        <div class="stat-label">Position</div>
                        <div class="stat-value" id="racePosition">1st</div>
                    </div>
                    <div class="race-stat">
                        <div class="stat-label">Best Lap</div>
                        <div class="stat-value" id="bestLap">--:--</div>
                    </div>
                    <div class="race-stat">
                        <div class="stat-label">Last Lap</div>
                        <div class="stat-value" id="lastLap">--:--</div>
                    </div>
                </div>

                <div class="lap-progress">
                    <div style="font-size: 0.9rem; margin-bottom: 0.5rem;">Lap Progress</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="lapProgress" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Leaderboard -->
            <div class="leaderboard">
                <div class="leaderboard-title">🏆 LEADERBOARD</div>
                <div id="leaderboardList">
                    <div class="racer-item">
                        <span class="racer-position">1.</span>
                        <span class="racer-name">You</span>
                        <span class="racer-time">0:00</span>
                    </div>
                    <div class="racer-item">
                        <span class="racer-position">2.</span>
                        <span class="racer-name">Speed Demon</span>
                        <span class="racer-time">0:05</span>
                    </div>
                    <div class="racer-item">
                        <span class="racer-position">3.</span>
                        <span class="racer-name">Turbo Racer</span>
                        <span class="racer-time">0:08</span>
                    </div>
                    <div class="racer-item">
                        <span class="racer-position">4.</span>
                        <span class="racer-name">Lightning</span>
                        <span class="racer-time">0:12</span>
                    </div>
                </div>
            </div>

            <!-- Speed Gauge -->
            <div class="speed-gauge">
                <div class="speed-needle" id="speedNeedle"></div>
                <div class="speed-display" id="speedDisplay">0</div>
                <div class="speed-unit">KM/H</div>
            </div>

            <!-- Race Timer -->
            <div class="race-timer">
                <div class="timer-label">Race Time</div>
                <div class="timer-value" id="raceTimer">0:00.0</div>
            </div>

            <!-- Boost Indicator -->
            <div class="boost-indicator">
                <div style="font-size: 0.8rem; margin-bottom: 0.5rem;">BOOST</div>
                <div class="boost-bar">
                    <div class="boost-fill" id="boostFill" style="height: 100%"></div>
                </div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem;">100%</div>
            </div>

            <!-- Minimap -->
            <div class="minimap">
                <canvas id="minimapCanvas" width="150" height="150"></canvas>
            </div>

            <!-- Controls -->
            <div class="race-controls">
                <strong>🏁 Racing Controls:</strong><br>
                WASD: Drive • SHIFT: Boost • C: Camera • R: Reset • SPACE: Brake
            </div>
        </div>

        <canvas id="canvas"></canvas>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Racing Game Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let raceTime = 0;
        let currentLap = 1;
        let totalLaps = 5;
        let lapProgress = 0;
        let racePosition = 1;
        let bestLapTime = null;
        let lastLapTime = null;
        let lapStartTime = 0;
        let boostCharge = 100;
        let isRaceStarted = false;
        let raceStartTime = 0;
        let cameraMode = 0;

        // Race track checkpoints
        let checkpoints = [];
        let currentCheckpoint = 0;
        let lapDistance = 0;
        let totalLapDistance = 1000; // meters

        // AI racers
        let aiRacers = [];
        let racerNames = ['Speed Demon', 'Turbo Racer', 'Lightning', 'Velocity'];

        const cameraModes = ['Third Person', 'First Person', 'Top Down', 'Cinematic'];

        // Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false,
            Shift: false, Space: false
        };

        function init() {
            console.log('🏁 Initializing Racing Championship...');

            // Hide loading screen and start countdown
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('hud').style.display = 'block';
                startRaceCountdown();
            }, 3000);

            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);
            scene.fog = new THREE.Fog(0x87CEEB, 50, 300);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

            // Create renderer
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('canvas'),
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Create race environment
            createRaceTrack();
            createRacingCar();
            createAIRacers();
            setupRaceLighting();
            setupEventListeners();
            initMinimap();

            // Start game loop
            animate();

            console.log('✅ Racing championship initialized!');
        }

        function createRaceTrack() {
            // Ground
            const groundGeometry = new THREE.PlaneGeometry(800, 800);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Race track (oval)
            createOvalTrack();

            // Track barriers
            createTrackBarriers();

            // Grandstands
            createGrandstands();

            // Start/finish line
            createStartFinishLine();

            // Checkpoints for lap detection
            createCheckpoints();
        }

        function createOvalTrack() {
            // Main track surface
            const trackWidth = 20;
            const trackLength = 400;

            // Straight sections
            const straightGeometry = new THREE.PlaneGeometry(trackLength, trackWidth);
            const trackMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            // Top straight
            const topStraight = new THREE.Mesh(straightGeometry, trackMaterial);
            topStraight.rotation.x = -Math.PI / 2;
            topStraight.position.set(0, 0.01, 100);
            topStraight.receiveShadow = true;
            scene.add(topStraight);

            // Bottom straight
            const bottomStraight = new THREE.Mesh(straightGeometry, trackMaterial);
            bottomStraight.rotation.x = -Math.PI / 2;
            bottomStraight.position.set(0, 0.01, -100);
            bottomStraight.receiveShadow = true;
            scene.add(bottomStraight);

            // Curved sections
            createTrackCurves(trackWidth);

            // Track markings
            createTrackMarkings();
        }

        function createTrackCurves(trackWidth) {
            const curveGeometry = new THREE.RingGeometry(90, 90 + trackWidth, 0, Math.PI);
            const trackMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            // Right curve
            const rightCurve = new THREE.Mesh(curveGeometry, trackMaterial);
            rightCurve.rotation.x = -Math.PI / 2;
            rightCurve.position.set(200, 0.01, 0);
            rightCurve.receiveShadow = true;
            scene.add(rightCurve);

            // Left curve
            const leftCurve = new THREE.Mesh(curveGeometry, trackMaterial);
            leftCurve.rotation.x = -Math.PI / 2;
            leftCurve.rotation.z = Math.PI;
            leftCurve.position.set(-200, 0.01, 0);
            leftCurve.receiveShadow = true;
            scene.add(leftCurve);
        }

        function createTrackMarkings() {
            const markingGeometry = new THREE.PlaneGeometry(8, 1);
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            // Center line markings on straights
            for (let i = -180; i < 180; i += 25) {
                const marking = new THREE.Mesh(markingGeometry, markingMaterial);
                marking.rotation.x = -Math.PI / 2;
                marking.position.set(i, 0.02, 100);
                scene.add(marking);

                const bottomMarking = new THREE.Mesh(markingGeometry, markingMaterial);
                bottomMarking.rotation.x = -Math.PI / 2;
                bottomMarking.position.set(i, 0.02, -100);
                scene.add(bottomMarking);
            }
        }

        function createTrackBarriers() {
            const barrierGeometry = new THREE.BoxGeometry(2, 3, 2);
            const barrierMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });

            // Outer barriers
            const barrierPositions = [
                // Top straight outer
                ...Array.from({length: 40}, (_, i) => ({x: -200 + i * 10, z: 120, y: 1.5})),
                // Bottom straight outer
                ...Array.from({length: 40}, (_, i) => ({x: -200 + i * 10, z: -120, y: 1.5})),
                // Right curve outer
                ...Array.from({length: 20}, (_, i) => {
                    const angle = i * Math.PI / 20;
                    return {
                        x: 200 + Math.cos(angle) * 120,
                        z: Math.sin(angle) * 120,
                        y: 1.5
                    };
                }),
                // Left curve outer
                ...Array.from({length: 20}, (_, i) => {
                    const angle = Math.PI + i * Math.PI / 20;
                    return {
                        x: -200 + Math.cos(angle) * 120,
                        z: Math.sin(angle) * 120,
                        y: 1.5
                    };
                })
            ];

            barrierPositions.forEach(pos => {
                const barrier = new THREE.Mesh(barrierGeometry, barrierMaterial);
                barrier.position.set(pos.x, pos.y, pos.z);
                barrier.castShadow = true;
                scene.add(barrier);
            });
        }

        function createGrandstands() {
            const grandstandConfigs = [
                { x: 0, z: 150, w: 100, h: 20, d: 15, color: 0x8B4513 },
                { x: 0, z: -150, w: 100, h: 20, d: 15, color: 0x696969 },
                { x: 250, z: 0, w: 15, h: 25, d: 80, color: 0x4682B4 },
                { x: -250, z: 0, w: 15, h: 25, d: 80, color: 0x8B0000 }
            ];

            grandstandConfigs.forEach(config => {
                const grandstandGeometry = new THREE.BoxGeometry(config.w, config.h, config.d);
                const grandstandMaterial = new THREE.MeshLambertMaterial({ color: config.color });
                const grandstand = new THREE.Mesh(grandstandGeometry, grandstandMaterial);
                grandstand.position.set(config.x, config.h / 2, config.z);
                grandstand.castShadow = true;
                grandstand.receiveShadow = true;
                scene.add(grandstand);

                // Add crowd effect (small colored cubes)
                addCrowd(grandstand, config);
            });
        }

        function addCrowd(grandstand, config) {
            const crowdColors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff];

            for (let i = 0; i < 50; i++) {
                const crowdGeometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
                const crowdMaterial = new THREE.MeshBasicMaterial({
                    color: crowdColors[Math.floor(Math.random() * crowdColors.length)]
                });
                const crowdMember = new THREE.Mesh(crowdGeometry, crowdMaterial);

                crowdMember.position.set(
                    (Math.random() - 0.5) * config.w * 0.8,
                    config.h * 0.3 + Math.random() * config.h * 0.4,
                    (Math.random() - 0.5) * config.d * 0.8
                );

                grandstand.add(crowdMember);
            }
        }

        function createStartFinishLine() {
            // Start/finish line
            const lineGeometry = new THREE.PlaneGeometry(20, 2);
            const lineMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.8
            });

            const startLine = new THREE.Mesh(lineGeometry, lineMaterial);
            startLine.rotation.x = -Math.PI / 2;
            startLine.position.set(-190, 0.03, 100);
            scene.add(startLine);

            // Checkered pattern
            for (let i = 0; i < 10; i++) {
                for (let j = 0; j < 10; j++) {
                    const checkerGeometry = new THREE.PlaneGeometry(2, 0.2);
                    const checkerMaterial = new THREE.MeshBasicMaterial({
                        color: (i + j) % 2 === 0 ? 0xffffff : 0x000000
                    });
                    const checker = new THREE.Mesh(checkerGeometry, checkerMaterial);
                    checker.rotation.x = -Math.PI / 2;
                    checker.position.set(-199 + i * 2, 0.04, 99 + j * 0.2);
                    scene.add(checker);
                }
            }
        }

        function createCheckpoints() {
            checkpoints = [
                { x: -190, z: 100 }, // Start/finish
                { x: 0, z: 100 },    // Top straight middle
                { x: 190, z: 100 },  // Top straight end
                { x: 200, z: 0 },    // Right curve
                { x: 190, z: -100 }, // Bottom straight start
                { x: 0, z: -100 },   // Bottom straight middle
                { x: -190, z: -100 },// Bottom straight end
                { x: -200, z: 0 }    // Left curve
            ];

            // Visual checkpoint markers (invisible in race)
            checkpoints.forEach((checkpoint, index) => {
                const markerGeometry = new THREE.CylinderGeometry(2, 2, 8);
                const markerMaterial = new THREE.MeshBasicMaterial({
                    color: 0x00ff00,
                    transparent: true,
                    opacity: 0.3
                });
                const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                marker.position.set(checkpoint.x, 4, checkpoint.z);
                marker.visible = false; // Hidden during race
                scene.add(marker);
            });
        }

        function createRacingCar() {
            // Racing car with aerodynamic design
            const carGeometry = new THREE.BoxGeometry(2, 0.8, 4.5);
            const carMaterial = new THREE.MeshLambertMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(-190, 1, 95); // Start position
            car.castShadow = true;
            scene.add(car);

            // Update car position
            carPosition.x = -190;
            carPosition.z = 95;

            // Add racing car details
            addRacingCarDetails();

            console.log('🏎️ Racing car created');
        }

        function addRacingCarDetails() {
            // Spoiler
            const spoilerGeometry = new THREE.BoxGeometry(2.2, 0.3, 0.5);
            const spoilerMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const spoiler = new THREE.Mesh(spoilerGeometry, spoilerMaterial);
            spoiler.position.set(0, 0.8, -2);
            car.add(spoiler);

            // Racing stripes
            const stripeGeometry = new THREE.BoxGeometry(0.3, 0.01, 4.5);
            const stripeMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            const leftStripe = new THREE.Mesh(stripeGeometry, stripeMaterial);
            leftStripe.position.set(-0.5, 0.41, 0);
            car.add(leftStripe);

            const rightStripe = new THREE.Mesh(stripeGeometry, stripeMaterial);
            rightStripe.position.set(0.5, 0.41, 0);
            car.add(rightStripe);

            // Racing wheels
            createRacingWheels();

            // Racing number
            addRacingNumber();
        }

        function createRacingWheels() {
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 12);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });

            const rimGeometry = new THREE.CylinderGeometry(0.25, 0.25, 0.35, 12);
            const rimMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });

            const wheelPositions = [
                { x: -1.1, y: -0.3, z: 1.8 },   // Front left
                { x: 1.1, y: -0.3, z: 1.8 },    // Front right
                { x: -1.1, y: -0.3, z: -1.8 },  // Rear left
                { x: 1.1, y: -0.3, z: -1.8 }    // Rear right
            ];

            car.wheels = [];
            wheelPositions.forEach(pos => {
                // Wheel
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                wheel.castShadow = true;
                car.add(wheel);

                // Rim
                const rim = new THREE.Mesh(rimGeometry, rimMaterial);
                rim.rotation.z = Math.PI / 2;
                rim.position.set(pos.x, pos.y, pos.z);
                car.add(rim);

                car.wheels.push({ wheel, rim });
            });
        }

        function addRacingNumber() {
            const numberGeometry = new THREE.PlaneGeometry(1, 1);
            const numberMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.9
            });

            // Number on roof
            const roofNumber = new THREE.Mesh(numberGeometry, numberMaterial);
            roofNumber.rotation.x = -Math.PI / 2;
            roofNumber.position.set(0, 0.41, 0);
            car.add(roofNumber);

            // Numbers on sides
            const leftNumber = new THREE.Mesh(numberGeometry, numberMaterial);
            leftNumber.position.set(-1.01, 0.2, 0);
            leftNumber.rotation.y = Math.PI / 2;
            car.add(leftNumber);

            const rightNumber = new THREE.Mesh(numberGeometry, numberMaterial);
            rightNumber.position.set(1.01, 0.2, 0);
            rightNumber.rotation.y = -Math.PI / 2;
            car.add(rightNumber);
        }

        function createAIRacers() {
            const aiColors = [0x0066cc, 0x00cc66, 0xcc6600, 0x6600cc];
            const startPositions = [
                { x: -190, z: 90 },   // Behind player
                { x: -190, z: 85 },   // Further behind
                { x: -190, z: 80 },   // Even further
                { x: -190, z: 75 }    // Last
            ];

            aiRacers = [];

            for (let i = 0; i < 4; i++) {
                const aiCar = createAICar(aiColors[i], startPositions[i], i);
                aiRacers.push({
                    car: aiCar,
                    position: { ...startPositions[i], y: 1 },
                    velocity: { x: 0, z: 0 },
                    rotation: 0,
                    speed: 0,
                    lapTime: 0,
                    currentLap: 1,
                    checkpoint: 0,
                    name: racerNames[i],
                    lapProgress: 0
                });
                scene.add(aiCar);
            }
        }

        function createAICar(color, position, index) {
            const carGeometry = new THREE.BoxGeometry(1.8, 0.7, 4);
            const carMaterial = new THREE.MeshLambertMaterial({ color: color });
            const aiCar = new THREE.Mesh(carGeometry, carMaterial);
            aiCar.position.set(position.x, 1, position.z);
            aiCar.castShadow = true;

            // Add simple wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.35, 0.35, 0.25, 8);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            const wheelPositions = [
                { x: -0.9, y: -0.25, z: 1.5 },
                { x: 0.9, y: -0.25, z: 1.5 },
                { x: -0.9, y: -0.25, z: -1.5 },
                { x: 0.9, y: -0.25, z: -1.5 }
            ];

            aiCar.wheels = [];
            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                aiCar.add(wheel);
                aiCar.wheels.push(wheel);
            });

            return aiCar;
        }

        function setupRaceLighting() {
            // Bright racing lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -400;
            directionalLight.shadow.camera.right = 400;
            directionalLight.shadow.camera.top = 400;
            directionalLight.shadow.camera.bottom = -400;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 500;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Stadium lights
            createStadiumLights();
        }

        function createStadiumLights() {
            const lightPositions = [
                { x: 0, y: 40, z: 150 },
                { x: 0, y: 40, z: -150 },
                { x: 250, y: 40, z: 0 },
                { x: -250, y: 40, z: 0 }
            ];

            lightPositions.forEach(pos => {
                const spotLight = new THREE.SpotLight(0xffffff, 0.8, 200, Math.PI / 6);
                spotLight.position.set(pos.x, pos.y, pos.z);
                spotLight.target.position.set(0, 0, 0);
                spotLight.castShadow = true;
                scene.add(spotLight);
                scene.add(spotLight.target);

                // Light fixture
                const fixtureGeometry = new THREE.CylinderGeometry(2, 3, 4);
                const fixtureMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
                const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
                fixture.position.set(pos.x, pos.y, pos.z);
                scene.add(fixture);
            });
        }

        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code === 'ShiftLeft' || event.code === 'ShiftRight' ? 'Shift' :
                           event.code === 'Space' ? 'Space' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }

                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }

                event.preventDefault();
            });

            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code === 'ShiftLeft' || event.code === 'ShiftRight' ? 'Shift' :
                           event.code === 'Space' ? 'Space' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function startRaceCountdown() {
            let countdown = 3;

            function showCountdown() {
                if (countdown > 0) {
                    showRaceNotification(countdown.toString(), 'countdown');
                    countdown--;
                    setTimeout(showCountdown, 1000);
                } else {
                    showRaceNotification('GO!', 'countdown');
                    setTimeout(() => {
                        isRaceStarted = true;
                        raceStartTime = Date.now();
                        lapStartTime = Date.now();
                        showRaceNotification('🏁 Race Started!');
                    }, 1000);
                }
            }

            showCountdown();
        }

        function updateRacingCar() {
            if (!isRaceStarted) return;

            const baseAcceleration = 0.06;
            const boostMultiplier = (keys.Shift && boostCharge > 0) ? 2.5 : 1.0;
            const acceleration = baseAcceleration * boostMultiplier;
            const maxSpeed = keys.Shift ? 1.8 : 1.2;
            const brakeFriction = keys.Space ? 0.8 : 0.97;
            const turnSpeed = 0.05;

            let engineForce = 0;
            let steerAngle = 0;

            // Handle input
            if (keys.w || keys.ArrowUp) {
                engineForce = acceleration;
            }
            if (keys.s || keys.ArrowDown) {
                engineForce = -acceleration * 0.6;
            }
            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;

            // Boost system
            if (keys.Shift && boostCharge > 0) {
                boostCharge = Math.max(boostCharge - 0.8, 0);
                createBoostParticles();
            } else {
                boostCharge = Math.min(boostCharge + 0.2, 100);
            }

            // Update velocity
            if (Math.abs(engineForce) > 0.001) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;
            }

            // Apply friction
            carVelocity.x *= brakeFriction;
            carVelocity.z *= brakeFriction;

            // Limit speed
            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }

            // Update rotation
            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.02) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }

            // Update position
            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;

            // Update car mesh
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;

            // Animate wheels
            if (car.wheels) {
                const wheelRotation = currentSpeed * 8;
                car.wheels.forEach((wheelSet, index) => {
                    wheelSet.wheel.rotation.x += wheelRotation;
                    wheelSet.rim.rotation.x += wheelRotation;

                    // Front wheels steering
                    if (index < 2) {
                        wheelSet.wheel.rotation.y = steerAngle * 4;
                        wheelSet.rim.rotation.y = steerAngle * 4;
                    }
                });
            }

            // Calculate speed
            speed = currentSpeed * 150; // Racing speed scale

            // Check checkpoints and lap progress
            updateLapProgress();
        }

        function updateAIRacers() {
            if (!isRaceStarted) return;

            aiRacers.forEach((racer, index) => {
                // Simple AI movement along track
                const targetCheckpoint = checkpoints[racer.checkpoint];
                const dx = targetCheckpoint.x - racer.position.x;
                const dz = targetCheckpoint.z - racer.position.z;
                const distance = Math.sqrt(dx * dx + dz * dz);

                if (distance < 15) {
                    racer.checkpoint = (racer.checkpoint + 1) % checkpoints.length;
                    if (racer.checkpoint === 0) {
                        racer.currentLap++;
                        racer.lapTime = (Date.now() - raceStartTime) / 1000;
                    }
                }

                // AI speed varies by racer
                const aiSpeed = 0.8 + index * 0.1 + Math.random() * 0.2;
                const moveX = (dx / distance) * aiSpeed;
                const moveZ = (dz / distance) * aiSpeed;

                racer.position.x += moveX;
                racer.position.z += moveZ;
                racer.speed = Math.sqrt(moveX * moveX + moveZ * moveZ) * 150;

                // Update AI car rotation
                racer.rotation = Math.atan2(moveX, moveZ);

                // Update AI car mesh
                racer.car.position.set(racer.position.x, racer.position.y, racer.position.z);
                racer.car.rotation.y = racer.rotation;

                // Animate AI car wheels
                if (racer.car.wheels) {
                    const wheelRotation = racer.speed * 0.05;
                    racer.car.wheels.forEach(wheel => {
                        wheel.rotation.x += wheelRotation;
                    });
                }
            });
        }

        function updateLapProgress() {
            // Check if player passed through checkpoints
            const currentCheckpointPos = checkpoints[currentCheckpoint];
            const dx = carPosition.x - currentCheckpointPos.x;
            const dz = carPosition.z - currentCheckpointPos.z;
            const distance = Math.sqrt(dx * dx + dz * dz);

            if (distance < 20) {
                currentCheckpoint = (currentCheckpoint + 1) % checkpoints.length;

                // Completed a lap
                if (currentCheckpoint === 0) {
                    completeLap();
                }
            }

            // Calculate lap progress percentage
            lapProgress = (currentCheckpoint / checkpoints.length) * 100;
        }

        function completeLap() {
            const lapTime = (Date.now() - lapStartTime) / 1000;
            lastLapTime = lapTime;

            if (!bestLapTime || lapTime < bestLapTime) {
                bestLapTime = lapTime;
                showRaceNotification('🏆 New Best Lap!');
            }

            currentLap++;
            lapStartTime = Date.now();

            if (currentLap > totalLaps) {
                finishRace();
            } else {
                showRaceNotification(`Lap ${currentLap}/${totalLaps} - ${formatTime(lapTime)}`);
            }
        }

        function finishRace() {
            isRaceStarted = false;
            const totalTime = (Date.now() - raceStartTime) / 1000;
            showRaceNotification(`🏁 Race Finished! Total Time: ${formatTime(totalTime)}`);
        }

        function updateRacingCamera() {
            const smoothness = 0.12;
            let targetX, targetY, targetZ, lookAtX, lookAtY, lookAtZ;

            switch(cameraMode) {
                case 0: // Racing Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 12;
                    targetY = carPosition.y + 6;
                    targetZ = carPosition.z - Math.cos(carRotation) * 12;
                    lookAtX = carPosition.x + Math.sin(carRotation) * 5;
                    lookAtY = carPosition.y + 1;
                    lookAtZ = carPosition.z + Math.cos(carRotation) * 5;
                    break;

                case 1: // Racing First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 1;
                    targetY = carPosition.y + 1.2;
                    targetZ = carPosition.z + Math.cos(carRotation) * 1;
                    lookAtX = carPosition.x + Math.sin(carRotation) * 30;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z + Math.cos(carRotation) * 30;
                    break;

                case 2: // Track Overview
                    targetX = 0;
                    targetY = 80;
                    targetZ = 0;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z;
                    break;

                case 3: // Cinematic Racing
                    const time = Date.now() * 0.001;
                    const radius = 25;
                    targetX = carPosition.x + Math.cos(time * 0.5) * radius;
                    targetY = carPosition.y + 8 + Math.sin(time * 0.3) * 4;
                    targetZ = carPosition.z + Math.sin(time * 0.5) * radius;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y + 2;
                    lookAtZ = carPosition.z;
                    break;
            }

            // Smooth camera movement
            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(lookAtX, lookAtY, lookAtZ);
        }

        function createBoostParticles() {
            for (let i = 0; i < 3; i++) {
                const particleGeometry = new THREE.SphereGeometry(0.1, 6, 6);
                const particleMaterial = new THREE.MeshBasicMaterial({
                    color: 0x00ffff,
                    transparent: true,
                    opacity: 0.8
                });
                const particle = new THREE.Mesh(particleGeometry, particleMaterial);

                // Position behind car
                const particleX = carPosition.x - Math.sin(carRotation) * 3 + (Math.random() - 0.5) * 2;
                const particleZ = carPosition.z - Math.cos(carRotation) * 3 + (Math.random() - 0.5) * 2;
                particle.position.set(particleX, carPosition.y + 0.3, particleZ);

                scene.add(particle);

                // Animate and remove
                let opacity = 0.8;
                const animate = () => {
                    opacity -= 0.05;
                    particle.material.opacity = opacity;
                    particle.position.y += 0.1;
                    particle.scale.multiplyScalar(1.05);

                    if (opacity <= 0) {
                        scene.remove(particle);
                    } else {
                        requestAnimationFrame(animate);
                    }
                };
                animate();
            }
        }

        function updateLeaderboard() {
            // Create combined racer list
            const allRacers = [
                {
                    name: 'You',
                    lapTime: lastLapTime || 0,
                    currentLap: currentLap,
                    position: carPosition
                },
                ...aiRacers
            ];

            // Sort by lap and progress
            allRacers.sort((a, b) => {
                if (a.currentLap !== b.currentLap) {
                    return b.currentLap - a.currentLap;
                }
                // If same lap, sort by checkpoint progress
                return b.checkpoint - a.checkpoint;
            });

            // Update leaderboard display
            const leaderboardList = document.getElementById('leaderboardList');
            leaderboardList.innerHTML = '';

            allRacers.forEach((racer, index) => {
                const item = document.createElement('div');
                item.className = 'racer-item';
                item.innerHTML = `
                    <span class="racer-position">${index + 1}.</span>
                    <span class="racer-name">${racer.name}</span>
                    <span class="racer-time">${formatTime(racer.lapTime)}</span>
                `;
                leaderboardList.appendChild(item);

                if (racer.name === 'You') {
                    racePosition = index + 1;
                }
            });
        }

        function initMinimap() {
            const minimapCanvas = document.getElementById('minimapCanvas');
            const ctx = minimapCanvas.getContext('2d');

            // Draw track outline
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 8;
            ctx.beginPath();

            // Scale factor for minimap
            const scale = 0.15;
            const offsetX = 75;
            const offsetY = 75;

            // Draw oval track
            ctx.ellipse(offsetX, offsetY, 60, 30, 0, 0, 2 * Math.PI);
            ctx.stroke();
        }

        function updateMinimap() {
            const minimapCanvas = document.getElementById('minimapCanvas');
            const ctx = minimapCanvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, 150, 150);

            // Redraw track
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.ellipse(75, 75, 60, 30, 0, 0, 2 * Math.PI);
            ctx.stroke();

            // Draw player car
            const scale = 0.15;
            const playerX = 75 + carPosition.x * scale;
            const playerY = 75 + carPosition.z * scale;

            ctx.fillStyle = '#ff4444';
            ctx.beginPath();
            ctx.arc(playerX, playerY, 4, 0, 2 * Math.PI);
            ctx.fill();

            // Draw AI cars
            aiRacers.forEach((racer, index) => {
                const aiX = 75 + racer.position.x * scale;
                const aiY = 75 + racer.position.z * scale;

                ctx.fillStyle = ['#0066cc', '#00cc66', '#cc6600', '#6600cc'][index];
                ctx.beginPath();
                ctx.arc(aiX, aiY, 3, 0, 2 * Math.PI);
                ctx.fill();
            });
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            showRaceNotification(`📷 ${cameraModes[cameraMode]} Camera`);
        }

        function resetCar() {
            carPosition = { x: -190, y: 1, z: 95 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            currentCheckpoint = 0;
            showRaceNotification('🔄 Car Reset');
        }

        function showRaceNotification(message, type = 'normal') {
            const notification = document.createElement('div');
            notification.className = type === 'countdown' ? 'countdown' : 'race-notification';
            notification.textContent = message;
            document.getElementById('gameContainer').appendChild(notification);

            const duration = type === 'countdown' ? 1000 : 3000;
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, duration);
        }

        function formatTime(seconds) {
            if (!seconds) return '--:--';
            const minutes = Math.floor(seconds / 60);
            const secs = (seconds % 60).toFixed(1);
            return `${minutes}:${secs.padStart(4, '0')}`;
        }

        function updateRacingUI() {
            if (!isRaceStarted) return;

            // Update race time
            raceTime = (Date.now() - raceStartTime) / 1000;
            document.getElementById('raceTimer').textContent = formatTime(raceTime);

            // Update lap info
            document.getElementById('currentLap').textContent = `${currentLap}/${totalLaps}`;
            document.getElementById('racePosition').textContent = getPositionSuffix(racePosition);
            document.getElementById('bestLap').textContent = formatTime(bestLapTime);
            document.getElementById('lastLap').textContent = formatTime(lastLapTime);

            // Update lap progress
            document.getElementById('lapProgress').style.width = `${lapProgress}%`;

            // Update speed gauge
            document.getElementById('speedDisplay').textContent = Math.round(speed);
            const speedNeedle = document.getElementById('speedNeedle');
            const angle = (speed / 300) * 180 - 90; // 0-300 km/h range
            speedNeedle.style.transform = `rotate(${angle}deg)`;

            // Update boost indicator
            document.getElementById('boostFill').style.height = `${boostCharge}%`;

            // Update leaderboard
            updateLeaderboard();

            // Update minimap
            updateMinimap();
        }

        function getPositionSuffix(position) {
            const suffixes = ['st', 'nd', 'rd', 'th'];
            const suffix = suffixes[Math.min(position - 1, 3)];
            return `${position}${suffix}`;
        }

        function animate() {
            requestAnimationFrame(animate);

            updateRacingCar();
            updateAIRacers();
            updateRacingCamera();
            updateRacingUI();

            renderer.render(scene, camera);
        }

        window.addEventListener('load', () => {
            console.log('🏁 Starting Racing Championship...');
            init();
        });
    </script>
</body>
</html>
