<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Car Customizer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        canvas {
            display: block;
        }

        .customizer-panel {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            z-index: 100;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .panel-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00f5ff;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .customization-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(0, 245, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #00f5ff;
            margin-bottom: 1rem;
            text-align: center;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
            border-color: #00f5ff;
        }

        .color-option.selected {
            border-color: #ffffff;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .slider-container {
            margin-bottom: 1rem;
        }

        .slider-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.3);
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #00f5ff;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #00f5ff;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        .preset-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .preset-btn {
            padding: 0.8rem;
            background: rgba(0, 245, 255, 0.2);
            border: 1px solid rgba(0, 245, 255, 0.5);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .preset-btn:hover {
            background: rgba(0, 245, 255, 0.4);
            transform: translateY(-2px);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00f5ff, #0080ff);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        .camera-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            z-index: 100;
        }

        .camera-btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: rgba(0, 245, 255, 0.2);
            border: 1px solid rgba(0, 245, 255, 0.5);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .camera-btn:hover {
            background: rgba(0, 245, 255, 0.4);
        }

        .camera-btn:last-child {
            margin-bottom: 0;
        }

        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem 2rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            text-align: center;
            z-index: 100;
        }

        .loading-customizer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
        }

        .loading-title {
            font-size: 3rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #00f5ff, #ff4444, #44ff44);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .loading-progress {
            width: 300px;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 2rem 0;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #00f5ff, #ff4444);
            border-radius: 3px;
            animation: loading 2s ease-in-out;
        }

        @keyframes loading {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            text-align: center;
            z-index: 200;
            animation: fadeInOut 3s ease-in-out;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading" class="loading-customizer">
            <div class="loading-title">🎨 CAR CUSTOMIZER</div>
            <p style="font-size: 1.2rem; margin-bottom: 1rem;">Loading 3D Car Editor...</p>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
            <p style="opacity: 0.8;">Customize your dream car with colors, wheels, and more!</p>
        </div>

        <div class="customizer-panel" id="customizerPanel" style="display: none;">
            <div class="panel-title">🎨 Car Customizer</div>

            <!-- Car Colors -->
            <div class="customization-section">
                <div class="section-title">🚗 Car Color</div>
                <div class="color-grid" id="carColors">
                    <div class="color-option selected" style="background: #ff4444;" data-color="0xff4444"></div>
                    <div class="color-option" style="background: #4444ff;" data-color="0x4444ff"></div>
                    <div class="color-option" style="background: #44ff44;" data-color="0x44ff44"></div>
                    <div class="color-option" style="background: #ffff44;" data-color="0xffff44"></div>
                    <div class="color-option" style="background: #ff44ff;" data-color="0xff44ff"></div>
                    <div class="color-option" style="background: #44ffff;" data-color="0x44ffff"></div>
                    <div class="color-option" style="background: #ffffff;" data-color="0xffffff"></div>
                    <div class="color-option" style="background: #000000;" data-color="0x000000"></div>
                    <div class="color-option" style="background: #ff8844;" data-color="0xff8844"></div>
                    <div class="color-option" style="background: #8844ff;" data-color="0x8844ff"></div>
                    <div class="color-option" style="background: #44ff88;" data-color="0x44ff88"></div>
                    <div class="color-option" style="background: #888888;" data-color="0x888888"></div>
                </div>
            </div>

            <!-- Wheel Colors -->
            <div class="customization-section">
                <div class="section-title">⚙️ Wheel Color</div>
                <div class="color-grid" id="wheelColors">
                    <div class="color-option selected" style="background: #333333;" data-color="0x333333"></div>
                    <div class="color-option" style="background: #888888;" data-color="0x888888"></div>
                    <div class="color-option" style="background: #cccccc;" data-color="0xcccccc"></div>
                    <div class="color-option" style="background: #ff4444;" data-color="0xff4444"></div>
                    <div class="color-option" style="background: #4444ff;" data-color="0x4444ff"></div>
                    <div class="color-option" style="background: #44ff44;" data-color="0x44ff44"></div>
                    <div class="color-option" style="background: #ffff44;" data-color="0xffff44"></div>
                    <div class="color-option" style="background: #000000;" data-color="0x000000"></div>
                </div>
            </div>

            <!-- Car Scale -->
            <div class="customization-section">
                <div class="section-title">📏 Car Size</div>
                <div class="slider-container">
                    <div class="slider-label">
                        <span>Scale</span>
                        <span id="scaleValue">1.0x</span>
                    </div>
                    <input type="range" class="slider" id="scaleSlider" min="0.5" max="2.0" step="0.1" value="1.0">
                </div>
            </div>

            <!-- Presets -->
            <div class="customization-section">
                <div class="section-title">🎯 Quick Presets</div>
                <div class="preset-buttons">
                    <button class="preset-btn" onclick="applyPreset('sports')">🏎️ Sports Car</button>
                    <button class="preset-btn" onclick="applyPreset('luxury')">💎 Luxury</button>
                    <button class="preset-btn" onclick="applyPreset('racing')">🏁 Racing</button>
                    <button class="preset-btn" onclick="applyPreset('classic')">🚙 Classic</button>
                </div>
            </div>

            <!-- Actions -->
            <div class="action-buttons">
                <button class="action-btn btn-secondary" onclick="resetCustomization()">🔄 Reset</button>
                <button class="action-btn btn-primary" onclick="saveCustomization()">💾 Save</button>
            </div>
        </div>

        <div class="camera-controls" id="cameraControls" style="display: none;">
            <button class="camera-btn" onclick="switchCamera()">📷 Change View</button>
            <button class="camera-btn" onclick="autoRotate()">🔄 Auto Rotate</button>
            <button class="camera-btn" onclick="resetCamera()">🎯 Reset Camera</button>
        </div>

        <div class="info-panel" id="infoPanel" style="display: none;">
            <strong>🎨 Car Customizer:</strong> Click colors to customize • Use sliders to adjust • Try presets for quick styles
        </div>

        <canvas id="canvas"></canvas>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Customizer Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let cameraMode = 0;
        let autoRotateEnabled = false;
        let rotationSpeed = 0.01;

        // Customization state
        let carColor = 0xff4444;
        let wheelColor = 0x333333;
        let carScale = 1.0;

        const cameraModes = ['Orbit', 'Front', 'Side', 'Rear', 'Top'];

        function init() {
            console.log('🎨 Initializing Car Customizer...');

            // Hide loading screen
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('customizerPanel').style.display = 'block';
                document.getElementById('cameraControls').style.display = 'block';
                document.getElementById('infoPanel').style.display = 'block';
                showNotification('🎨 Car Customizer Ready!');
            }, 2000);

            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(8, 5, 8);
            camera.lookAt(0, 1, 0);

            // Create renderer
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('canvas'),
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Create environment
            createShowroom();
            createCustomizableCar();
            setupLighting();
            setupEventListeners();

            // Start render loop
            animate();

            console.log('✅ Car customizer initialized!');
        }

        function createShowroom() {
            // Showroom floor
            const floorGeometry = new THREE.PlaneGeometry(50, 50);
            const floorMaterial = new THREE.MeshLambertMaterial({
                color: 0xcccccc,
                transparent: true,
                opacity: 0.8
            });
            ground = new THREE.Mesh(floorGeometry, floorMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Showroom walls
            createShowroomWalls();

            // Display platform
            createDisplayPlatform();
        }

        function createShowroomWalls() {
            const wallMaterial = new THREE.MeshLambertMaterial({
                color: 0xf0f0f0,
                transparent: true,
                opacity: 0.3
            });

            // Back wall
            const backWallGeometry = new THREE.PlaneGeometry(50, 20);
            const backWall = new THREE.Mesh(backWallGeometry, wallMaterial);
            backWall.position.set(0, 10, -25);
            scene.add(backWall);

            // Side walls
            const sideWallGeometry = new THREE.PlaneGeometry(50, 20);

            const leftWall = new THREE.Mesh(sideWallGeometry, wallMaterial);
            leftWall.rotation.y = Math.PI / 2;
            leftWall.position.set(-25, 10, 0);
            scene.add(leftWall);

            const rightWall = new THREE.Mesh(sideWallGeometry, wallMaterial);
            rightWall.rotation.y = -Math.PI / 2;
            rightWall.position.set(25, 10, 0);
            scene.add(rightWall);
        }

        function createDisplayPlatform() {
            // Circular platform
            const platformGeometry = new THREE.CylinderGeometry(6, 6, 0.2, 32);
            const platformMaterial = new THREE.MeshLambertMaterial({
                color: 0x444444,
                transparent: true,
                opacity: 0.8
            });
            const platform = new THREE.Mesh(platformGeometry, platformMaterial);
            platform.position.set(0, 0.1, 0);
            platform.receiveShadow = true;
            scene.add(platform);

            // Platform edge lighting
            const edgeGeometry = new THREE.TorusGeometry(6, 0.1, 8, 32);
            const edgeMaterial = new THREE.MeshBasicMaterial({
                color: 0x00f5ff,
                emissive: 0x004455
            });
            const edge = new THREE.Mesh(edgeGeometry, edgeMaterial);
            edge.rotation.x = Math.PI / 2;
            edge.position.set(0, 0.2, 0);
            scene.add(edge);
        }

        function createCustomizableCar() {
            // Car body
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshLambertMaterial({ color: carColor });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.castShadow = true;
            scene.add(car);

            // Add car details
            addCarComponents();

            console.log('🚗 Customizable car created');
        }

        function addCarComponents() {
            // Clear existing components
            if (car.children.length > 0) {
                car.children.forEach(child => {
                    car.remove(child);
                });
            }

            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(1.8, 0.8, 0.1);
            const windshieldMaterial = new THREE.MeshLambertMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.2, 1.5);
            car.add(windshield);

            // Rear windshield
            const rearWindshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            rearWindshield.position.set(0, 0.2, -1.5);
            car.add(rearWindshield);

            // Side windows
            const sideWindowGeometry = new THREE.BoxGeometry(0.1, 0.6, 1.5);
            const leftWindow = new THREE.Mesh(sideWindowGeometry, windshieldMaterial);
            leftWindow.position.set(-1, 0.2, 0);
            car.add(leftWindow);

            const rightWindow = new THREE.Mesh(sideWindowGeometry, windshieldMaterial);
            rightWindow.position.set(1, 0.2, 0);
            car.add(rightWindow);

            // Roof
            const roofGeometry = new THREE.BoxGeometry(2, 0.1, 2.5);
            const roofMaterial = new THREE.MeshLambertMaterial({ color: carColor });
            const roof = new THREE.Mesh(roofGeometry, roofMaterial);
            roof.position.set(0, 0.55, 0);
            car.add(roof);

            // Headlights
            const headlightGeometry = new THREE.SphereGeometry(0.2, 8, 8);
            const headlightMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffaa,
                emissive: 0x222200
            });

            const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
            leftHeadlight.position.set(-0.7, 0, 2);
            car.add(leftHeadlight);

            const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
            rightHeadlight.position.set(0.7, 0, 2);
            car.add(rightHeadlight);

            // Taillights
            const taillightMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                emissive: 0x220000
            });

            const leftTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
            leftTaillight.position.set(-0.7, 0, -2);
            car.add(leftTaillight);

            const rightTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
            rightTaillight.position.set(0.7, 0, -2);
            car.add(rightTaillight);

            // Wheels
            createCustomizableWheels();
        }

        function createCustomizableWheels() {
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 12);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: wheelColor });

            const rimGeometry = new THREE.CylinderGeometry(0.25, 0.25, 0.35, 12);
            const rimMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });

            const wheelPositions = [
                { x: -1.1, y: -0.3, z: 1.5 },   // Front left
                { x: 1.1, y: -0.3, z: 1.5 },    // Front right
                { x: -1.1, y: -0.3, z: -1.5 },  // Rear left
                { x: 1.1, y: -0.3, z: -1.5 }    // Rear right
            ];

            car.wheels = [];
            wheelPositions.forEach(pos => {
                // Wheel
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                wheel.castShadow = true;
                car.add(wheel);

                // Rim
                const rim = new THREE.Mesh(rimGeometry, rimMaterial);
                rim.rotation.z = Math.PI / 2;
                rim.position.set(pos.x, pos.y, pos.z);
                car.add(rim);

                car.wheels.push({ wheel, rim });
            });
        }

        function setupLighting() {
            // Ambient light
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // Main directional light
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -20;
            directionalLight.shadow.camera.right = 20;
            directionalLight.shadow.camera.top = 20;
            directionalLight.shadow.camera.bottom = -20;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 50;
            directionalLight.shadow.mapSize.width = 1024;
            directionalLight.shadow.mapSize.height = 1024;
            scene.add(directionalLight);

            // Showroom spotlights
            createShowroomLights();
        }

        function createShowroomLights() {
            const spotlightPositions = [
                { x: 5, y: 15, z: 5 },
                { x: -5, y: 15, z: 5 },
                { x: 5, y: 15, z: -5 },
                { x: -5, y: 15, z: -5 }
            ];

            spotlightPositions.forEach(pos => {
                const spotLight = new THREE.SpotLight(0xffffff, 0.5, 30, Math.PI / 8);
                spotLight.position.set(pos.x, pos.y, pos.z);
                spotLight.target.position.set(0, 1, 0);
                spotLight.castShadow = true;
                scene.add(spotLight);
                scene.add(spotLight.target);
            });
        }

        function setupEventListeners() {
            // Car color selection
            document.getElementById('carColors').addEventListener('click', (e) => {
                if (e.target.classList.contains('color-option')) {
                    // Remove previous selection
                    document.querySelectorAll('#carColors .color-option').forEach(el => {
                        el.classList.remove('selected');
                    });

                    // Add selection to clicked color
                    e.target.classList.add('selected');

                    // Update car color
                    carColor = parseInt(e.target.dataset.color);
                    updateCarColor();
                }
            });

            // Wheel color selection
            document.getElementById('wheelColors').addEventListener('click', (e) => {
                if (e.target.classList.contains('color-option')) {
                    // Remove previous selection
                    document.querySelectorAll('#wheelColors .color-option').forEach(el => {
                        el.classList.remove('selected');
                    });

                    // Add selection to clicked color
                    e.target.classList.add('selected');

                    // Update wheel color
                    wheelColor = parseInt(e.target.dataset.color);
                    updateWheelColor();
                }
            });

            // Scale slider
            document.getElementById('scaleSlider').addEventListener('input', (e) => {
                carScale = parseFloat(e.target.value);
                document.getElementById('scaleValue').textContent = carScale.toFixed(1) + 'x';
                updateCarScale();
            });

            // Window resize
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function updateCarColor() {
            car.material.color.setHex(carColor);
            // Update roof color to match
            if (car.children.length > 4) {
                car.children[4].material.color.setHex(carColor);
            }
            showNotification('🎨 Car color updated!');
        }

        function updateWheelColor() {
            if (car.wheels) {
                car.wheels.forEach(wheelSet => {
                    wheelSet.wheel.material.color.setHex(wheelColor);
                });
            }
            showNotification('⚙️ Wheel color updated!');
        }

        function updateCarScale() {
            car.scale.set(carScale, carScale, carScale);
            showNotification(`📏 Car scaled to ${carScale.toFixed(1)}x`);
        }

        function applyPreset(presetName) {
            switch(presetName) {
                case 'sports':
                    carColor = 0xff4444;
                    wheelColor = 0x000000;
                    carScale = 1.2;
                    break;
                case 'luxury':
                    carColor = 0x000000;
                    wheelColor = 0xcccccc;
                    carScale = 1.1;
                    break;
                case 'racing':
                    carColor = 0xffff44;
                    wheelColor = 0xff4444;
                    carScale = 1.0;
                    break;
                case 'classic':
                    carColor = 0x8844ff;
                    wheelColor = 0x888888;
                    carScale = 0.9;
                    break;
            }

            // Update UI
            updateColorSelections();
            document.getElementById('scaleSlider').value = carScale;
            document.getElementById('scaleValue').textContent = carScale.toFixed(1) + 'x';

            // Apply changes
            updateCarColor();
            updateWheelColor();
            updateCarScale();

            showNotification(`🎯 ${presetName.charAt(0).toUpperCase() + presetName.slice(1)} preset applied!`);
        }

        function updateColorSelections() {
            // Update car color selection
            document.querySelectorAll('#carColors .color-option').forEach(el => {
                el.classList.remove('selected');
                if (parseInt(el.dataset.color) === carColor) {
                    el.classList.add('selected');
                }
            });

            // Update wheel color selection
            document.querySelectorAll('#wheelColors .color-option').forEach(el => {
                el.classList.remove('selected');
                if (parseInt(el.dataset.color) === wheelColor) {
                    el.classList.add('selected');
                }
            });
        }

        function resetCustomization() {
            carColor = 0xff4444;
            wheelColor = 0x333333;
            carScale = 1.0;

            updateColorSelections();
            document.getElementById('scaleSlider').value = carScale;
            document.getElementById('scaleValue').textContent = carScale.toFixed(1) + 'x';

            updateCarColor();
            updateWheelColor();
            updateCarScale();

            showNotification('🔄 Customization reset!');
        }

        function saveCustomization() {
            const customization = {
                carColor: carColor,
                wheelColor: wheelColor,
                carScale: carScale,
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('carCustomization', JSON.stringify(customization));
            showNotification('💾 Customization saved!');
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            updateCameraPosition();
            showNotification(`📷 ${cameraModes[cameraMode]} View`);
        }

        function updateCameraPosition() {
            switch(cameraMode) {
                case 0: // Orbit
                    camera.position.set(8, 5, 8);
                    camera.lookAt(0, 1, 0);
                    break;
                case 1: // Front
                    camera.position.set(0, 2, 8);
                    camera.lookAt(0, 1, 0);
                    break;
                case 2: // Side
                    camera.position.set(8, 2, 0);
                    camera.lookAt(0, 1, 0);
                    break;
                case 3: // Rear
                    camera.position.set(0, 2, -8);
                    camera.lookAt(0, 1, 0);
                    break;
                case 4: // Top
                    camera.position.set(0, 15, 0);
                    camera.lookAt(0, 1, 0);
                    break;
            }
        }

        function autoRotate() {
            autoRotateEnabled = !autoRotateEnabled;
            showNotification(autoRotateEnabled ? '🔄 Auto rotate ON' : '🔄 Auto rotate OFF');
        }

        function resetCamera() {
            cameraMode = 0;
            autoRotateEnabled = false;
            updateCameraPosition();
            showNotification('🎯 Camera reset');
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.getElementById('gameContainer').appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        function animate() {
            requestAnimationFrame(animate);

            // Auto rotate car
            if (autoRotateEnabled) {
                carRotation += rotationSpeed;
                car.rotation.y = carRotation;
            }

            // Orbit camera mode
            if (cameraMode === 0 && !autoRotateEnabled) {
                const time = Date.now() * 0.0005;
                camera.position.x = Math.cos(time) * 10;
                camera.position.z = Math.sin(time) * 10;
                camera.lookAt(0, 1, 0);
            }

            renderer.render(scene, camera);
        }

        window.addEventListener('load', () => {
            console.log('🎨 Starting Car Customizer...');
            init();
        });
    </script>
</body>
</html>
