/**
 * Main Game Engine Class
 * Handles the core game loop, scene management, and coordination between systems
 */
class GameEngine {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.world = null; // Physics world
        
        this.car = null;
        this.environment = null;
        this.cameraController = null;
        
        this.isRunning = false;
        this.isPaused = false;
        
        this.clock = new THREE.Clock();
        this.score = 0;
        this.speed = 0;
        
        // Input handling
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false
        };
        
        this.init();
    }
    
    /**
     * Initialize the game engine
     */
    init() {
        this.setupRenderer();
        this.setupScene();
        this.setupPhysics();
        this.setupLighting();
        this.setupEventListeners();
        
        console.log('🚗 Game Engine initialized successfully!');
    }
    
    /**
     * Setup the Three.js renderer
     */
    setupRenderer() {
        const canvas = document.getElementById('game-canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        // Set background color
        this.renderer.setClearColor(0x87CEEB, 1); // Sky blue
    }
    
    /**
     * Setup the Three.js scene
     */
    setupScene() {
        this.scene = new THREE.Scene();
        
        // Add fog for depth perception
        this.scene.fog = new THREE.Fog(0x87CEEB, 100, 1000);
        
        // Setup camera
        this.camera = new THREE.PerspectiveCamera(
            75, // FOV
            window.innerWidth / window.innerHeight, // Aspect ratio
            0.1, // Near plane
            2000 // Far plane
        );
        
        this.camera.position.set(0, 5, 10);
    }
    
    /**
     * Setup Cannon.js physics world
     */
    setupPhysics() {
        this.world = new CANNON.World();
        this.world.gravity.set(0, -9.82, 0); // Earth gravity
        this.world.broadphase = new CANNON.NaiveBroadphase();
        this.world.solver.iterations = 10;
        
        // Ground contact material
        const groundMaterial = new CANNON.Material('ground');
        const wheelMaterial = new CANNON.Material('wheel');
        
        const wheelGroundContactMaterial = new CANNON.ContactMaterial(
            wheelMaterial,
            groundMaterial,
            {
                friction: 0.4,
                restitution: 0.3,
                contactEquationStiffness: 1000
            }
        );
        
        this.world.addContactMaterial(wheelGroundContactMaterial);
    }
    
    /**
     * Setup scene lighting
     */
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        
        // Shadow camera settings
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        
        this.scene.add(directionalLight);
        
        // Hemisphere light for natural lighting
        const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x362d1d, 0.5);
        this.scene.add(hemisphereLight);
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Keyboard input
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Prevent context menu on right click
        document.addEventListener('contextmenu', (event) => event.preventDefault());
    }
    
    /**
     * Handle key down events
     */
    onKeyDown(event) {
        if (this.isPaused) return;
        
        switch(event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.keys.forward = true;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.keys.backward = true;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.keys.left = true;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.keys.right = true;
                break;
            case 'KeyC':
                if (this.cameraController) {
                    this.cameraController.switchCamera();
                }
                break;
            case 'KeyR':
                if (this.car) {
                    this.car.reset();
                }
                break;
        }
    }
    
    /**
     * Handle key up events
     */
    onKeyUp(event) {
        switch(event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.keys.forward = false;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.keys.backward = false;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.keys.left = false;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.keys.right = false;
                break;
        }
    }
    
    /**
     * Handle window resize
     */
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    /**
     * Start the game
     */
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.isPaused = false;
        this.gameLoop();
        
        console.log('🎮 Game started!');
    }
    
    /**
     * Pause the game
     */
    pause() {
        this.isPaused = true;
        console.log('⏸️ Game paused');
    }
    
    /**
     * Resume the game
     */
    resume() {
        this.isPaused = false;
        console.log('▶️ Game resumed');
    }
    
    /**
     * Stop the game
     */
    stop() {
        this.isRunning = false;
        this.isPaused = false;
        console.log('🛑 Game stopped');
    }
    
    /**
     * Main game loop
     */
    gameLoop() {
        if (!this.isRunning) return;
        
        requestAnimationFrame(() => this.gameLoop());
        
        if (this.isPaused) return;
        
        const deltaTime = this.clock.getDelta();
        
        // Update physics
        this.world.step(deltaTime);
        
        // Update game objects
        if (this.car) {
            this.car.update(deltaTime, this.keys);
            this.speed = this.car.getSpeed();
        }
        
        if (this.environment) {
            this.environment.update(deltaTime);
        }
        
        if (this.cameraController) {
            this.cameraController.update(deltaTime);
        }
        
        // Update score (based on distance traveled)
        this.score += this.speed * deltaTime * 0.1;
        
        // Update UI
        this.updateUI();
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
    
    /**
     * Update UI elements
     */
    updateUI() {
        const speedDisplay = document.getElementById('speed-display');
        const scoreDisplay = document.getElementById('score');
        
        if (speedDisplay) {
            speedDisplay.textContent = Math.round(this.speed);
        }
        
        if (scoreDisplay) {
            scoreDisplay.textContent = Math.round(this.score);
        }
    }
    
    /**
     * Reset the game
     */
    reset() {
        this.score = 0;
        this.speed = 0;
        
        if (this.car) {
            this.car.reset();
        }
        
        console.log('🔄 Game reset');
    }
}
