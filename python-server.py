#!/usr/bin/env python3
"""
Car Driving Application - Python Server
Simple HTTP server to serve the car driving game on local network
"""

import http.server
import socketserver
import socket
import os
import sys
import webbrowser
from urllib.parse import urlparse
import mimetypes

# Server configuration
PORT = 3000
HOST = '0.0.0.0'  # Listen on all network interfaces

class CarDrivingHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for the car driving application"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers for better compatibility
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        # Redirect root to index page
        if parsed_path.path == '/':
            self.send_response(302)
            self.send_header('Location', '/simple-car.html')
            self.end_headers()
            return
        
        # Serve files normally
        super().do_GET()
    
    def log_message(self, format, *args):
        """Custom log format"""
        print(f"🌐 {self.address_string()} - {format % args}")

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote server to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
        return local_ip
    except Exception:
        return "localhost"

def check_files():
    """Check if the required game files exist"""
    required_files = [
        'simple-car.html',
        'enhanced-car.html', 
        'mobile-car.html',
        'index.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️  Warning: Some game files are missing:")
        for file in missing_files:
            print(f"   - {file}")
        print()
    
    return len(missing_files) == 0

def print_banner():
    """Print server startup banner"""
    print("\n" + "="*50)
    print("🚗 Car Driving Application Server")
    print("="*50)

def print_server_info(local_ip, port):
    """Print server information"""
    print(f"\n📍 Server started successfully!")
    print(f"📍 Local Access:   http://localhost:{port}")
    print(f"🌐 Network Access: http://{local_ip}:{port}")
    print("\n🎮 Available games:")
    print("   • Simple Car:    /simple-car.html")
    print("   • Enhanced Car:  /enhanced-car.html") 
    print("   • Mobile Car:    /mobile-car.html")
    print("   • Professional: /index.html")
    print(f"\n📱 Share http://{local_ip}:{port} with others on your network!")
    print("🛑 Press Ctrl+C to stop the server")
    print("="*50 + "\n")

def main():
    """Main server function"""
    print_banner()
    
    # Check Python version
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    
    # Check if game files exist
    check_files()
    
    # Get local IP
    local_ip = get_local_ip()
    
    # Try to find an available port
    port = PORT
    while port < PORT + 10:
        try:
            with socketserver.TCPServer((HOST, port), CarDrivingHTTPRequestHandler) as httpd:
                print_server_info(local_ip, port)
                
                # Try to open browser automatically
                try:
                    webbrowser.open(f'http://localhost:{port}')
                    print("🌐 Opening browser automatically...")
                except:
                    pass
                
                # Start serving
                httpd.serve_forever()
                
        except OSError as e:
            if e.errno == 98 or e.errno == 10048:  # Address already in use
                print(f"⚠️  Port {port} is already in use, trying {port + 1}...")
                port += 1
                continue
            else:
                print(f"❌ Error starting server: {e}")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)
    
    print(f"❌ Could not find an available port between {PORT} and {PORT + 9}")
    sys.exit(1)

if __name__ == "__main__":
    main()
