<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚗 Car Driving Application - Complete Overview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00f5ff, #ff4444, #44ff44);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 4s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 1rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .stat-item {
            background: rgba(0, 245, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #00f5ff;
        }
        
        .versions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .version-card {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .version-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #00f5ff, #ff4444, #44ff44, #ffaa00);
            background-size: 400% 400%;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: gradientMove 8s ease-in-out infinite;
            z-index: -1;
        }
        
        .version-card:hover::before {
            opacity: 0.1;
        }
        
        .version-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .version-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .version-icon {
            font-size: 3rem;
            margin-right: 1rem;
        }
        
        .version-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00f5ff;
        }
        
        .version-subtitle {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .features-list {
            list-style: none;
            margin: 1.5rem 0;
        }
        
        .features-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            margin-right: 0.5rem;
            color: #44ff44;
        }
        
        .version-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            flex: 1;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #00f5ff, #0080ff);
            color: white;
        }
        
        .btn-primary:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 245, 255, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .controls-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 3rem;
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 1.5rem;
        }
        
        .control-group {
            background: rgba(0, 245, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }
        
        .control-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #00f5ff;
        }
        
        .control-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-item:last-child {
            border-bottom: none;
        }
        
        .control-key {
            background: rgba(0, 0, 0, 0.5);
            padding: 0.3rem 0.6rem;
            border-radius: 5px;
            font-family: monospace;
            font-weight: bold;
            color: #00f5ff;
        }
        
        .server-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .server-status {
            display: inline-flex;
            align-items: center;
            background: rgba(68, 255, 68, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            border: 1px solid rgba(68, 255, 68, 0.5);
            margin-bottom: 1rem;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            background: #44ff44;
            border-radius: 50%;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .url-display {
            background: rgba(0, 0, 0, 0.5);
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            font-size: 1.1rem;
            margin: 0.5rem;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            opacity: 0.8;
        }
        
        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }
        
        .tech-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .versions-grid {
                grid-template-columns: 1fr;
            }
            
            .version-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🚗 Car Driving Application Suite</h1>
            <p class="subtitle">Professional 3D Car Driving Simulator with Multiple Versions</p>
            <p>Experience realistic car physics, stunning 3D graphics, and immersive gameplay</p>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div>Game Versions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div>Features</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3D</div>
                    <div>Graphics Engine</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div>Web-Based</div>
                </div>
            </div>
        </div>
        
        <!-- Server Status -->
        <div class="server-info">
            <div class="server-status">
                <div class="status-dot"></div>
                <span>Server Running</span>
            </div>
            <h3>🌐 Network Access</h3>
            <p>Share these URLs with others on your network:</p>
            <div class="url-display">Local: http://localhost:3000</div>
            <div class="url-display">Network: http://************:3000</div>
        </div>
        
        <!-- Game Versions -->
        <div class="versions-grid">
            <!-- Simple Car -->
            <div class="version-card">
                <div class="version-header">
                    <div class="version-icon">🚗</div>
                    <div>
                        <div class="version-title">Simple Car Driving</div>
                        <div class="version-subtitle">Clean & Reliable</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Guaranteed to work</li>
                    <li><span class="feature-icon">✅</span> Smooth car physics</li>
                    <li><span class="feature-icon">✅</span> Multiple camera views</li>
                    <li><span class="feature-icon">✅</span> Real-time HUD</li>
                    <li><span class="feature-icon">✅</span> Easy to understand</li>
                </ul>
                
                <div class="version-buttons">
                    <a href="/simple-car.html" class="btn btn-primary">🎮 Play Now</a>
                    <a href="#" class="btn btn-secondary" onclick="showCode('simple-car.html')">📝 View Code</a>
                </div>
            </div>
            
            <!-- Enhanced Car -->
            <div class="version-card">
                <div class="version-header">
                    <div class="version-icon">🏁</div>
                    <div>
                        <div class="version-title">Enhanced Car Simulator</div>
                        <div class="version-subtitle">Full-Featured Gaming</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Mission system</li>
                    <li><span class="feature-icon">✅</span> Traffic cars</li>
                    <li><span class="feature-icon">✅</span> Minimap</li>
                    <li><span class="feature-icon">✅</span> Visual effects</li>
                    <li><span class="feature-icon">✅</span> 4 camera modes</li>
                </ul>
                
                <div class="version-buttons">
                    <a href="/enhanced-car.html" class="btn btn-primary">🎮 Play Now</a>
                    <a href="#" class="btn btn-secondary" onclick="showCode('enhanced-car.html')">📝 View Code</a>
                </div>
            </div>
            
            <!-- Mobile Car -->
            <div class="version-card">
                <div class="version-header">
                    <div class="version-icon">📱</div>
                    <div>
                        <div class="version-title">Mobile Car Driving</div>
                        <div class="version-subtitle">Touch-Optimized</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Virtual joysticks</li>
                    <li><span class="feature-icon">✅</span> Mobile-friendly UI</li>
                    <li><span class="feature-icon">✅</span> Touch controls</li>
                    <li><span class="feature-icon">✅</span> Responsive design</li>
                    <li><span class="feature-icon">✅</span> Performance optimized</li>
                </ul>
                
                <div class="version-buttons">
                    <a href="/mobile-car.html" class="btn btn-primary">🎮 Play Now</a>
                    <a href="#" class="btn btn-secondary" onclick="showCode('mobile-car.html')">📝 View Code</a>
                </div>
            </div>
            
            <!-- Professional Version -->
            <div class="version-card">
                <div class="version-header">
                    <div class="version-icon">⚙️</div>
                    <div>
                        <div class="version-title">Professional Version</div>
                        <div class="version-subtitle">Advanced Architecture</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Modular design</li>
                    <li><span class="feature-icon">✅</span> Professional UI</li>
                    <li><span class="feature-icon">✅</span> Loading screens</li>
                    <li><span class="feature-icon">✅</span> Complex environment</li>
                    <li><span class="feature-icon">✅</span> Expandable code</li>
                </ul>
                
                <div class="version-buttons">
                    <a href="/index.html" class="btn btn-primary">🎮 Play Now</a>
                    <a href="#" class="btn btn-secondary" onclick="showCode('index.html')">📝 View Code</a>
                </div>
            </div>
            
            <!-- Advanced Car -->
            <div class="version-card">
                <div class="version-header">
                    <div class="version-icon">🏎️</div>
                    <div>
                        <div class="version-title">Advanced Car Simulator</div>
                        <div class="version-subtitle">Ultimate Experience</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Weather system</li>
                    <li><span class="feature-icon">✅</span> Day/night cycle</li>
                    <li><span class="feature-icon">✅</span> Achievement system</li>
                    <li><span class="feature-icon">✅</span> Advanced dashboard</li>
                    <li><span class="feature-icon">✅</span> Particle effects</li>
                </ul>
                
                <div class="version-buttons">
                    <a href="/advanced-car.html" class="btn btn-primary">🎮 Play Now</a>
                    <a href="#" class="btn btn-secondary" onclick="showCode('advanced-car.html')">📝 View Code</a>
                </div>
            </div>
        </div>
        
        <!-- Controls Guide -->
        <div class="controls-section">
            <h2>🎮 Controls Guide</h2>
            <div class="controls-grid">
                <div class="control-group">
                    <div class="control-title">Basic Controls</div>
                    <div class="control-item">
                        <span>Accelerate</span>
                        <span class="control-key">W / ↑</span>
                    </div>
                    <div class="control-item">
                        <span>Brake/Reverse</span>
                        <span class="control-key">S / ↓</span>
                    </div>
                    <div class="control-item">
                        <span>Turn Left</span>
                        <span class="control-key">A / ←</span>
                    </div>
                    <div class="control-item">
                        <span>Turn Right</span>
                        <span class="control-key">D / →</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <div class="control-title">Camera & System</div>
                    <div class="control-item">
                        <span>Change Camera</span>
                        <span class="control-key">C</span>
                    </div>
                    <div class="control-item">
                        <span>Reset Car</span>
                        <span class="control-key">R</span>
                    </div>
                    <div class="control-item">
                        <span>Toggle Lights</span>
                        <span class="control-key">L</span>
                    </div>
                    <div class="control-item">
                        <span>Night Mode</span>
                        <span class="control-key">N</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <div class="control-title">Advanced Features</div>
                    <div class="control-item">
                        <span>Turbo Boost</span>
                        <span class="control-key">T</span>
                    </div>
                    <div class="control-item">
                        <span>Handbrake</span>
                        <span class="control-key">B / Space</span>
                    </div>
                    <div class="control-item">
                        <span>Change Weather</span>
                        <span class="control-key">M</span>
                    </div>
                    <div class="control-item">
                        <span>Next Mission</span>
                        <span class="control-key">M</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <h3>🛠️ Technology Stack</h3>
            <div class="tech-stack">
                <div class="tech-item">Three.js 3D Engine</div>
                <div class="tech-item">WebGL Graphics</div>
                <div class="tech-item">HTML5 Canvas</div>
                <div class="tech-item">CSS3 Animations</div>
                <div class="tech-item">JavaScript ES6+</div>
                <div class="tech-item">Node.js Server</div>
            </div>
            
            <p style="margin-top: 2rem;">
                🚗 Car Driving Application Suite - Professional 3D Web-Based Driving Simulator<br>
                Built with modern web technologies for maximum compatibility and performance
            </p>
        </div>
    </div>
    
    <script>
        function showCode(filename) {
            window.open(`/${filename}`, '_blank');
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', () => {
            // Animate version cards on scroll
            const cards = document.querySelectorAll('.version-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
