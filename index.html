<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Car Driving Simulator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="car-icon">🚗</div>
            <h1>Car Driving Simulator</h1>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p>Loading assets...</p>
        </div>
    </div>

    <!-- Main Menu -->
    <div id="main-menu" class="hidden">
        <div class="menu-content">
            <h1 class="game-title">
                <span class="title-car">🚗</span>
                Car Driving Simulator
            </h1>
            <div class="menu-buttons">
                <button id="start-game" class="menu-btn primary">Start Driving</button>
                <button id="controls-btn" class="menu-btn">Controls</button>
                <button id="settings-btn" class="menu-btn">Settings</button>
            </div>
        </div>
    </div>

    <!-- Controls Modal -->
    <div id="controls-modal" class="modal hidden">
        <div class="modal-content">
            <h2>Controls</h2>
            <div class="controls-grid">
                <div class="control-item">
                    <span class="key">W / ↑</span>
                    <span class="action">Accelerate</span>
                </div>
                <div class="control-item">
                    <span class="key">S / ↓</span>
                    <span class="action">Brake / Reverse</span>
                </div>
                <div class="control-item">
                    <span class="key">A / ←</span>
                    <span class="action">Turn Left</span>
                </div>
                <div class="control-item">
                    <span class="key">D / →</span>
                    <span class="action">Turn Right</span>
                </div>
                <div class="control-item">
                    <span class="key">C</span>
                    <span class="action">Change Camera</span>
                </div>
                <div class="control-item">
                    <span class="key">R</span>
                    <span class="action">Reset Car</span>
                </div>
            </div>
            <button id="close-controls" class="menu-btn">Close</button>
        </div>
    </div>

    <!-- Game UI -->
    <div id="game-ui" class="hidden">
        <!-- HUD -->
        <div class="hud">
            <div class="hud-left">
                <div class="speed-meter">
                    <div class="meter-label">Speed</div>
                    <div class="meter-value" id="speed-display">0</div>
                    <div class="meter-unit">km/h</div>
                </div>
            </div>
            
            <div class="hud-center">
                <div class="score-display">
                    <span>Score: </span>
                    <span id="score">0</span>
                </div>
            </div>
            
            <div class="hud-right">
                <div class="camera-info">
                    <span id="camera-mode">Third Person</span>
                </div>
            </div>
        </div>

        <!-- Mini Controls -->
        <div class="mini-controls">
            <button id="pause-btn" class="control-btn">⏸️</button>
            <button id="camera-btn" class="control-btn">📷</button>
            <button id="reset-btn" class="control-btn">🔄</button>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <p>Use WASD or Arrow Keys to drive • Press C to change camera • Press R to reset</p>
        </div>
    </div>

    <!-- Pause Menu -->
    <div id="pause-menu" class="modal hidden">
        <div class="modal-content">
            <h2>Game Paused</h2>
            <div class="menu-buttons">
                <button id="resume-btn" class="menu-btn primary">Resume</button>
                <button id="restart-btn" class="menu-btn">Restart</button>
                <button id="main-menu-btn" class="menu-btn">Main Menu</button>
            </div>
        </div>
    </div>

    <!-- 3D Canvas -->
    <canvas id="game-canvas"></canvas>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cannon.js/0.20.0/cannon.min.js"></script>
    <script src="js/GameEngine.js"></script>
    <script src="js/CarController.js"></script>
    <script src="js/Environment.js"></script>
    <script src="js/CameraController.js"></script>
    <script src="js/UI.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
