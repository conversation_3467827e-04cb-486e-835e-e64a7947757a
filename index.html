<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Car Driving Simulator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="car-icon">🚗</div>
            <h1>Car Driving Simulator</h1>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p>Loading assets...</p>
        </div>
    </div>

    <!-- Main Menu -->
    <div id="main-menu" class="hidden">
        <div class="menu-content">
            <h1 class="game-title">
                <span class="title-car">🚗</span>
                Car Driving Simulator
            </h1>
            <div class="menu-buttons">
                <button id="start-game" class="menu-btn primary">Start Driving</button>
                <button id="controls-btn" class="menu-btn">Controls</button>
                <button id="settings-btn" class="menu-btn">Settings</button>
            </div>
        </div>
    </div>

    <!-- Controls Modal -->
    <div id="controls-modal" class="modal hidden">
        <div class="modal-content">
            <h2>Controls</h2>
            <div class="controls-grid">
                <div class="control-item">
                    <span class="key">W / ↑</span>
                    <span class="action">Accelerate</span>
                </div>
                <div class="control-item">
                    <span class="key">S / ↓</span>
                    <span class="action">Brake / Reverse</span>
                </div>
                <div class="control-item">
                    <span class="key">A / ←</span>
                    <span class="action">Turn Left</span>
                </div>
                <div class="control-item">
                    <span class="key">D / →</span>
                    <span class="action">Turn Right</span>
                </div>
                <div class="control-item">
                    <span class="key">C</span>
                    <span class="action">Change Camera</span>
                </div>
                <div class="control-item">
                    <span class="key">R</span>
                    <span class="action">Reset Car</span>
                </div>
            </div>
            <button id="close-controls" class="menu-btn">Close</button>
        </div>
    </div>

    <!-- Game UI -->
    <div id="game-ui" class="hidden">
        <!-- HUD -->
        <div class="hud">
            <div class="hud-left">
                <div class="speed-meter">
                    <div class="meter-label">Speed</div>
                    <div class="meter-value" id="speed-display">0</div>
                    <div class="meter-unit">km/h</div>
                </div>
            </div>

            <div class="hud-center">
                <div class="score-display">
                    <span>Score: </span>
                    <span id="score">0</span>
                </div>
            </div>

            <div class="hud-right">
                <div class="camera-info">
                    <span id="camera-mode">Third Person</span>
                </div>
            </div>
        </div>

        <!-- Mini Controls -->
        <div class="mini-controls">
            <button id="pause-btn" class="control-btn">⏸️</button>
            <button id="camera-btn" class="control-btn">📷</button>
            <button id="reset-btn" class="control-btn">🔄</button>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <p>Use WASD or Arrow Keys to drive • Press C to change camera • Press R to reset</p>
        </div>
    </div>

    <!-- Pause Menu -->
    <div id="pause-menu" class="modal hidden">
        <div class="modal-content">
            <h2>Game Paused</h2>
            <div class="menu-buttons">
                <button id="resume-btn" class="menu-btn primary">Resume</button>
                <button id="restart-btn" class="menu-btn">Restart</button>
                <button id="main-menu-btn" class="menu-btn">Main Menu</button>
            </div>
        </div>
    </div>

    <!-- 3D Canvas -->
    <canvas id="game-canvas"></canvas>

    <!-- Scripts -->
    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Simple working car game for index.html
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let score = 0;
        let gameTime = 0;
        let cameraMode = 0;
        const cameraModes = ['Third Person', 'First Person', 'Top Down'];

        // Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false
        };

        function init() {
            console.log('🚗 Initializing Car Game...');

            // Hide loading screen and show game UI
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('main-menu').style.display = 'none';
                document.getElementById('game-ui').style.display = 'block';
            }, 2000);

            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 8, -15);

            // Create renderer
            const canvas = document.getElementById('game-canvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);

            // Create environment
            createEnvironment();
            createCar();
            setupLighting();
            setupEventListeners();

            // Start game loop
            animate();

            console.log('✅ Game initialized successfully!');
        }

        function createEnvironment() {
            // Ground
            const groundGeometry = new THREE.PlaneGeometry(200, 200);
            const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x4a5d23 });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            scene.add(ground);

            // Road
            const roadGeometry = new THREE.PlaneGeometry(200, 15);
            const roadMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
            const road = new THREE.Mesh(roadGeometry, roadMaterial);
            road.rotation.x = -Math.PI / 2;
            road.position.y = 0.01;
            scene.add(road);

            // Road markings
            const markingGeometry = new THREE.PlaneGeometry(3, 0.4);
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            for (let i = -100; i < 100; i += 10) {
                const marking = new THREE.Mesh(markingGeometry, markingMaterial);
                marking.rotation.x = -Math.PI / 2;
                marking.position.set(i, 0.02, 0);
                scene.add(marking);
            }

            // Simple buildings
            const buildingPositions = [
                { x: 30, z: 30, h: 20 }, { x: -30, z: 30, h: 15 },
                { x: 30, z: -30, h: 25 }, { x: -30, z: -30, h: 18 }
            ];

            buildingPositions.forEach(pos => {
                const buildingGeometry = new THREE.BoxGeometry(10, pos.h, 8);
                const buildingMaterial = new THREE.MeshBasicMaterial({
                    color: Math.random() * 0x888888 + 0x444444
                });
                const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
                building.position.set(pos.x, pos.h / 2, pos.z);
                scene.add(building);
            });
        }

        function createCar() {
            // Car body
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshBasicMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            scene.add(car);

            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(1.8, 0.8, 0.1);
            const windshieldMaterial = new THREE.MeshBasicMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.5
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.2, 1.5);
            car.add(windshield);

            // Wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 8);
            const wheelMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });

            const wheelPositions = [
                { x: -1, y: -0.3, z: 1.2 },
                { x: 1, y: -0.3, z: 1.2 },
                { x: -1, y: -0.3, z: -1.2 },
                { x: 1, y: -0.3, z: -1.2 }
            ];

            car.wheels = [];
            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                car.add(wheel);
                car.wheels.push(wheel);
            });

            console.log('🚗 Car created at position:', car.position);
        }

        function setupLighting() {
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
        }

        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }

                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }
            });

            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function updateCar() {
            const acceleration = 0.025;
            const maxSpeed = 0.6;
            const friction = 0.96;
            const turnSpeed = 0.03;

            let engineForce = 0;
            let steerAngle = 0;

            if (keys.w || keys.ArrowUp) engineForce = acceleration;
            if (keys.s || keys.ArrowDown) engineForce = -acceleration * 0.5;
            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;

            if (Math.abs(engineForce) > 0.001) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;
            }

            carVelocity.x *= friction;
            carVelocity.z *= friction;

            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }

            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.01) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }

            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;

            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;

            if (car.wheels) {
                const wheelRotation = currentSpeed * 5;
                car.wheels.forEach((wheel, index) => {
                    wheel.rotation.x += wheelRotation;
                    if (index < 2) {
                        wheel.rotation.y = steerAngle * 3;
                    }
                });
            }

            speed = currentSpeed * 100;
            score += currentSpeed * 0.05;
        }

        function updateCamera() {
            const smoothness = 0.1;
            let targetX, targetY, targetZ;

            switch(cameraMode) {
                case 0: // Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 10;
                    targetY = carPosition.y + 5;
                    targetZ = carPosition.z - Math.cos(carRotation) * 10;
                    break;
                case 1: // First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 0.5;
                    targetY = carPosition.y + 1;
                    targetZ = carPosition.z + Math.cos(carRotation) * 0.5;
                    break;
                case 2: // Top Down
                    targetX = carPosition.x;
                    targetY = carPosition.y + 20;
                    targetZ = carPosition.z;
                    break;
            }

            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(carPosition.x, carPosition.y, carPosition.z);
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            const cameraModeElement = document.getElementById('camera-mode');
            if (cameraModeElement) {
                cameraModeElement.textContent = cameraModes[cameraMode];
            }
        }

        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
        }

        function updateUI() {
            gameTime += 1/60;
            const minutes = Math.floor(gameTime / 60);
            const seconds = Math.floor(gameTime % 60);

            const speedDisplay = document.getElementById('speed-display');
            const scoreDisplay = document.getElementById('score');

            if (speedDisplay) {
                speedDisplay.textContent = Math.round(speed);
            }
            if (scoreDisplay) {
                scoreDisplay.textContent = Math.round(score);
            }
        }

        function animate() {
            requestAnimationFrame(animate);

            updateCar();
            updateCamera();
            updateUI();

            renderer.render(scene, camera);
        }

        // Start the game
        window.addEventListener('load', () => {
            console.log('🚀 Starting Car Game...');
            init();
        });
    </script>
</body>
</html>
