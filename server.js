/**
 * Local Server for Car Driving Application
 * Serves the car driving game on local network
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Server configuration
const PORT = 3000;
const HOST = '0.0.0.0'; // Listen on all network interfaces

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

/**
 * Get the local IP address
 */
function getLocalIP() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
            if (interface.family === 'IPv4' && !interface.internal) {
                return interface.address;
            }
        }
    }
    return 'localhost';
}

/**
 * Serve static files
 */
function serveStaticFile(res, filePath, ext) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <html>
                    <head><title>404 - File Not Found</title></head>
                    <body style="font-family: Arial; text-align: center; padding: 50px;">
                        <h1>🚗 Car Driving Server</h1>
                        <h2>404 - File Not Found</h2>
                        <p>The requested file could not be found.</p>
                        <a href="/">← Back to Home</a>
                    </body>
                </html>
            `);
            return;
        }

        const mimeType = mimeTypes[ext] || 'application/octet-stream';
        res.writeHead(200, { 'Content-Type': mimeType });
        res.end(data);
    });
}

/**
 * Generate directory listing
 */
function generateDirectoryListing(dirPath, urlPath) {
    const files = fs.readdirSync(dirPath);
    const fileList = files
        .filter(file => !file.startsWith('.')) // Hide hidden files
        .map(file => {
            const filePath = path.join(dirPath, file);
            const stats = fs.statSync(filePath);
            const isDirectory = stats.isDirectory();
            const size = isDirectory ? '-' : `${(stats.size / 1024).toFixed(1)} KB`;
            const modified = stats.mtime.toLocaleDateString();
            
            return {
                name: file,
                isDirectory,
                size,
                modified,
                url: path.join(urlPath, file).replace(/\\/g, '/')
            };
        })
        .sort((a, b) => {
            // Directories first, then files
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });

    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🚗 Car Driving Server</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    min-height: 100vh;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background: rgba(0, 0, 0, 0.3);
                    padding: 2rem;
                    border-radius: 15px;
                    backdrop-filter: blur(10px);
                }
                h1 {
                    text-align: center;
                    margin-bottom: 2rem;
                    font-size: 2.5rem;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                }
                .games-section {
                    margin-bottom: 3rem;
                }
                .games-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }
                .game-card {
                    background: rgba(0, 0, 0, 0.5);
                    padding: 1.5rem;
                    border-radius: 10px;
                    border: 2px solid #00f5ff;
                    text-decoration: none;
                    color: white;
                    transition: all 0.3s ease;
                }
                .game-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 10px 20px rgba(0, 245, 255, 0.3);
                    background: rgba(0, 245, 255, 0.1);
                }
                .game-title {
                    font-size: 1.3rem;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    color: #00f5ff;
                }
                .game-description {
                    font-size: 0.9rem;
                    line-height: 1.4;
                    opacity: 0.9;
                }
                .files-section h2 {
                    border-bottom: 2px solid #00f5ff;
                    padding-bottom: 0.5rem;
                    margin-bottom: 1rem;
                }
                .file-list {
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 10px;
                    overflow: hidden;
                }
                .file-item {
                    display: flex;
                    align-items: center;
                    padding: 1rem;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    text-decoration: none;
                    color: white;
                    transition: background 0.3s ease;
                }
                .file-item:hover {
                    background: rgba(0, 245, 255, 0.1);
                }
                .file-item:last-child {
                    border-bottom: none;
                }
                .file-icon {
                    font-size: 1.5rem;
                    margin-right: 1rem;
                    width: 30px;
                    text-align: center;
                }
                .file-info {
                    flex: 1;
                }
                .file-name {
                    font-weight: bold;
                    margin-bottom: 0.2rem;
                }
                .file-details {
                    font-size: 0.8rem;
                    opacity: 0.7;
                }
                .server-info {
                    text-align: center;
                    margin-top: 2rem;
                    padding: 1rem;
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 10px;
                    border: 1px solid rgba(0, 245, 255, 0.3);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚗 Car Driving Application Server</h1>
                
                <div class="games-section">
                    <h2>🎮 Available Games</h2>
                    <div class="games-grid">
                        <a href="/simple-car.html" class="game-card">
                            <div class="game-title">🚗 Simple Car Driving</div>
                            <div class="game-description">
                                Clean and reliable car driving experience. Perfect for getting started with smooth physics and multiple camera views.
                            </div>
                        </a>
                        
                        <a href="/enhanced-car.html" class="game-card">
                            <div class="game-title">🏁 Enhanced Car Simulator</div>
                            <div class="game-description">
                                Full-featured driving experience with missions, traffic cars, minimap, and advanced visual effects.
                            </div>
                        </a>
                        
                        <a href="/mobile-car.html" class="game-card">
                            <div class="game-title">📱 Mobile Car Driving</div>
                            <div class="game-description">
                                Touch-optimized version with virtual joysticks. Perfect for mobile devices and tablets.
                            </div>
                        </a>
                        
                        <a href="/index.html" class="game-card">
                            <div class="game-title">⚙️ Professional Version</div>
                            <div class="game-description">
                                Advanced architecture with modular design. Great for developers and complex features.
                            </div>
                        </a>
                    </div>
                </div>
                
                <div class="files-section">
                    <h2>📁 All Files</h2>
                    <div class="file-list">
                        ${fileList.map(file => `
                            <a href="${file.url}" class="file-item">
                                <div class="file-icon">
                                    ${file.isDirectory ? '📁' : getFileIcon(file.name)}
                                </div>
                                <div class="file-info">
                                    <div class="file-name">${file.name}</div>
                                    <div class="file-details">
                                        ${file.size} • Modified: ${file.modified}
                                    </div>
                                </div>
                            </a>
                        `).join('')}
                    </div>
                </div>
                
                <div class="server-info">
                    <p><strong>Server Status:</strong> ✅ Running</p>
                    <p><strong>Local Access:</strong> <code>http://localhost:${PORT}</code></p>
                    <p><strong>Network Access:</strong> <code>http://${getLocalIP()}:${PORT}</code></p>
                    <p><em>Share the network URL with others on your local network to let them play!</em></p>
                </div>
            </div>
        </body>
        </html>
    `;
}

/**
 * Get appropriate icon for file type
 */
function getFileIcon(filename) {
    const ext = path.extname(filename).toLowerCase();
    const icons = {
        '.html': '🌐',
        '.js': '📜',
        '.css': '🎨',
        '.json': '📋',
        '.md': '📝',
        '.txt': '📄',
        '.png': '🖼️',
        '.jpg': '🖼️',
        '.gif': '🖼️',
        '.svg': '🖼️'
    };
    return icons[ext] || '📄';
}

/**
 * Create HTTP server
 */
const server = http.createServer((req, res) => {
    // Parse URL
    let urlPath = req.url;
    if (urlPath === '/') {
        urlPath = '/index.html';
    }

    // Remove query parameters
    urlPath = urlPath.split('?')[0];

    // Construct file path
    const filePath = path.join(__dirname, urlPath);

    // Security check - prevent directory traversal
    if (!filePath.startsWith(__dirname)) {
        res.writeHead(403, { 'Content-Type': 'text/html' });
        res.end('<h1>403 - Forbidden</h1>');
        return;
    }

    // Check if file/directory exists
    fs.stat(filePath, (err, stats) => {
        if (err) {
            // File doesn't exist, show directory listing for root
            if (urlPath === '/index.html') {
                res.writeHead(200, { 'Content-Type': 'text/html' });
                res.end(generateDirectoryListing(__dirname, '/'));
                return;
            }
            
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end('<h1>404 - File Not Found</h1>');
            return;
        }

        if (stats.isDirectory()) {
            // Serve directory listing
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(generateDirectoryListing(filePath, urlPath));
        } else {
            // Serve file
            const ext = path.extname(filePath);
            serveStaticFile(res, filePath, ext);
        }
    });
});

/**
 * Start server
 */
server.listen(PORT, HOST, () => {
    const localIP = getLocalIP();
    
    console.log('\n🚗 Car Driving Application Server Started!');
    console.log('==========================================');
    console.log(`📍 Local Access:   http://localhost:${PORT}`);
    console.log(`🌐 Network Access: http://${localIP}:${PORT}`);
    console.log('==========================================');
    console.log('\n📱 Share the network URL with others on your local network!');
    console.log('🎮 Available games:');
    console.log('   • Simple Car:    /simple-car.html');
    console.log('   • Enhanced Car:  /enhanced-car.html');
    console.log('   • Mobile Car:    /mobile-car.html');
    console.log('   • Professional: /index.html');
    console.log('\n🛑 Press Ctrl+C to stop the server\n');
});

/**
 * Handle server shutdown gracefully
 */
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server stopped successfully!');
        process.exit(0);
    });
});

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (err) => {
    console.error('❌ Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
