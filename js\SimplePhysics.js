/**
 * Simple Physics Engine
 * A lightweight physics system for the car driving application
 */

class SimplePhysics {
    constructor() {
        this.gravity = -9.82;
        this.bodies = [];
        this.groundLevel = 0;
    }
    
    /**
     * Add a physics body
     */
    addBody(body) {
        this.bodies.push(body);
    }
    
    /**
     * Remove a physics body
     */
    removeBody(body) {
        const index = this.bodies.indexOf(body);
        if (index > -1) {
            this.bodies.splice(index, 1);
        }
    }
    
    /**
     * Update physics simulation
     */
    step(deltaTime) {
        this.bodies.forEach(body => {
            if (body.mass > 0) {
                // Apply gravity
                body.velocity.y += this.gravity * deltaTime;
                
                // Update position
                body.position.x += body.velocity.x * deltaTime;
                body.position.y += body.velocity.y * deltaTime;
                body.position.z += body.velocity.z * deltaTime;
                
                // Ground collision
                if (body.position.y < this.groundLevel + body.height / 2) {
                    body.position.y = this.groundLevel + body.height / 2;
                    body.velocity.y = 0;
                }
                
                // Apply friction
                body.velocity.x *= 0.98;
                body.velocity.z *= 0.98;
            }
        });
    }
}

/**
 * Physics Body Class
 */
class PhysicsBody {
    constructor(options = {}) {
        this.position = new THREE.Vector3(
            options.x || 0,
            options.y || 0,
            options.z || 0
        );
        
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.rotation = new THREE.Euler(0, 0, 0);
        this.angularVelocity = new THREE.Vector3(0, 0, 0);
        
        this.mass = options.mass || 1;
        this.width = options.width || 1;
        this.height = options.height || 1;
        this.depth = options.depth || 1;
        
        this.friction = options.friction || 0.1;
        this.restitution = options.restitution || 0.3;
    }
    
    /**
     * Apply force to the body
     */
    applyForce(force) {
        if (this.mass > 0) {
            this.velocity.x += force.x / this.mass;
            this.velocity.y += force.y / this.mass;
            this.velocity.z += force.z / this.mass;
        }
    }
    
    /**
     * Set position
     */
    setPosition(x, y, z) {
        this.position.set(x, y, z);
    }
    
    /**
     * Set velocity
     */
    setVelocity(x, y, z) {
        this.velocity.set(x, y, z);
    }
    
    /**
     * Reset body
     */
    reset(x = 0, y = 2, z = 0) {
        this.position.set(x, y, z);
        this.velocity.set(0, 0, 0);
        this.rotation.set(0, 0, 0);
        this.angularVelocity.set(0, 0, 0);
    }
}

/**
 * Car Physics Body with special car mechanics
 */
class CarPhysicsBody extends PhysicsBody {
    constructor(options = {}) {
        super(options);
        
        // Car-specific properties
        this.engineForce = 0;
        this.brakeForce = 0;
        this.steerAngle = 0;
        this.maxSpeed = options.maxSpeed || 50;
        this.acceleration = options.acceleration || 20;
        this.braking = options.braking || 30;
        this.steering = options.steering || 2;
        
        // Wheel properties
        this.wheelBase = 3; // Distance between front and rear axles
        this.trackWidth = 1.8; // Distance between left and right wheels
        
        this.speed = 0;
    }
    
    /**
     * Update car physics
     */
    update(deltaTime, input) {
        // Handle input
        this.handleInput(input);
        
        // Calculate forces
        this.updateForces(deltaTime);
        
        // Update position and rotation
        this.updateMovement(deltaTime);
        
        // Calculate current speed
        this.speed = Math.sqrt(
            this.velocity.x * this.velocity.x + 
            this.velocity.z * this.velocity.z
        ) * 3.6; // Convert to km/h
    }
    
    /**
     * Handle player input
     */
    handleInput(input) {
        // Engine force
        if (input.forward) {
            this.engineForce = this.acceleration;
        } else if (input.backward) {
            this.engineForce = -this.acceleration * 0.5; // Reverse is slower
        } else {
            this.engineForce = 0;
        }
        
        // Steering
        if (input.left) {
            this.steerAngle = this.steering;
        } else if (input.right) {
            this.steerAngle = -this.steering;
        } else {
            this.steerAngle = 0;
        }
        
        // Braking
        if (!input.forward && !input.backward) {
            this.brakeForce = this.braking * 0.1; // Light braking when no input
        } else {
            this.brakeForce = 0;
        }
    }
    
    /**
     * Update forces acting on the car
     */
    updateForces(deltaTime) {
        // Get current forward direction
        const forward = new THREE.Vector3(0, 0, 1);
        forward.applyEuler(this.rotation);
        
        // Apply engine force
        if (this.engineForce !== 0) {
            const force = forward.clone().multiplyScalar(this.engineForce);
            this.applyForce(force);
        }
        
        // Apply braking
        if (this.brakeForce > 0) {
            const brakeDirection = this.velocity.clone().normalize().multiplyScalar(-this.brakeForce);
            this.applyForce(brakeDirection);
        }
        
        // Speed limit
        const currentSpeed = this.velocity.length();
        if (currentSpeed > this.maxSpeed) {
            this.velocity.normalize().multiplyScalar(this.maxSpeed);
        }
    }
    
    /**
     * Update car movement and rotation
     */
    updateMovement(deltaTime) {
        // Simple car steering model
        if (Math.abs(this.steerAngle) > 0.01 && this.velocity.length() > 0.1) {
            const steerFactor = this.steerAngle * deltaTime * (this.velocity.length() / this.maxSpeed);
            this.rotation.y += steerFactor;
        }
        
        // Apply air resistance
        this.velocity.multiplyScalar(0.995);
        
        // Limit steering angle return
        this.steerAngle *= 0.9;
    }
    
    /**
     * Get car speed in km/h
     */
    getSpeed() {
        return this.speed;
    }
    
    /**
     * Reset car to starting position
     */
    reset(x = 0, y = 2, z = 0) {
        super.reset(x, y, z);
        this.engineForce = 0;
        this.brakeForce = 0;
        this.steerAngle = 0;
        this.speed = 0;
    }
}
