<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Car Driving Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            color: white;
            overflow: hidden;
        }
        
        #info {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 1rem;
            border-radius: 10px;
            z-index: 100;
        }
        
        #canvas {
            display: block;
        }
        
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">
        <h2>🚗 Loading Car Driving App...</h2>
        <p>Please wait while we initialize the 3D engine...</p>
    </div>
    
    <div id="info" style="display: none;">
        <h3>🎮 Controls</h3>
        <p>WASD or Arrow Keys to drive</p>
        <p>C to change camera</p>
        <p>R to reset car</p>
        <div id="status">Status: Loading...</div>
    </div>
    
    <canvas id="canvas"></canvas>
    
    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Simple test to verify Three.js loads
        console.log('🔧 Testing Three.js...');
        
        if (typeof THREE !== 'undefined') {
            console.log('✅ Three.js loaded successfully!');
            document.getElementById('loading').innerHTML = '<h2>✅ Three.js Loaded!</h2><p>Initializing game...</p>';
            
            // Initialize a simple scene
            setTimeout(() => {
                initGame();
            }, 1000);
        } else {
            console.error('❌ Three.js failed to load');
            document.getElementById('loading').innerHTML = '<h2>❌ Error</h2><p>Failed to load 3D engine. Please refresh.</p>';
        }
        
        function initGame() {
            try {
                // Hide loading screen
                document.getElementById('loading').style.display = 'none';
                document.getElementById('info').style.display = 'block';
                
                // Create scene
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('canvas') });
                
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x87CEEB);
                
                // Create a simple car (cube)
                const carGeometry = new THREE.BoxGeometry(2, 1, 4);
                const carMaterial = new THREE.MeshBasicMaterial({ color: 0xff4444 });
                const car = new THREE.Mesh(carGeometry, carMaterial);
                car.position.y = 0.5;
                scene.add(car);
                
                // Create ground
                const groundGeometry = new THREE.PlaneGeometry(100, 100);
                const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x4a5d23 });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                scene.add(ground);
                
                // Position camera
                camera.position.set(0, 5, 10);
                camera.lookAt(car.position);
                
                // Simple controls
                const keys = { w: false, a: false, s: false, d: false };
                
                document.addEventListener('keydown', (e) => {
                    switch(e.code) {
                        case 'KeyW': keys.w = true; break;
                        case 'KeyA': keys.a = true; break;
                        case 'KeyS': keys.s = true; break;
                        case 'KeyD': keys.d = true; break;
                    }
                });
                
                document.addEventListener('keyup', (e) => {
                    switch(e.code) {
                        case 'KeyW': keys.w = false; break;
                        case 'KeyA': keys.a = false; break;
                        case 'KeyS': keys.s = false; break;
                        case 'KeyD': keys.d = false; break;
                    }
                });
                
                // Simple movement
                let speed = 0;
                let rotation = 0;
                
                function animate() {
                    requestAnimationFrame(animate);
                    
                    // Handle movement
                    if (keys.w) speed = Math.min(speed + 0.01, 0.2);
                    else if (keys.s) speed = Math.max(speed - 0.01, -0.1);
                    else speed *= 0.95;
                    
                    if (keys.a) rotation += 0.02;
                    if (keys.d) rotation -= 0.02;
                    
                    // Update car
                    car.rotation.y = rotation;
                    car.position.x += Math.sin(rotation) * speed;
                    car.position.z += Math.cos(rotation) * speed;
                    
                    // Update camera
                    camera.position.x = car.position.x - Math.sin(rotation) * 10;
                    camera.position.z = car.position.z - Math.cos(rotation) * 10;
                    camera.lookAt(car.position);
                    
                    // Update status
                    document.getElementById('status').textContent = 
                        `Speed: ${(Math.abs(speed) * 100).toFixed(0)} | Position: ${car.position.x.toFixed(1)}, ${car.position.z.toFixed(1)}`;
                    
                    renderer.render(scene, camera);
                }
                
                animate();
                console.log('🎮 Simple car game initialized!');
                
            } catch (error) {
                console.error('❌ Game initialization failed:', error);
                document.getElementById('loading').innerHTML = '<h2>❌ Error</h2><p>Game failed to initialize: ' + error.message + '</p>';
                document.getElementById('loading').style.display = 'block';
            }
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (typeof camera !== 'undefined' && typeof renderer !== 'undefined') {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });
    </script>
</body>
</html>
