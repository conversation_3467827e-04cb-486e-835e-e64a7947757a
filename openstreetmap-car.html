<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗺️ OpenStreetMap Car Driving</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            color: white;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        canvas {
            display: block;
        }

        .osm-hud {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .location-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #2ecc71;
            width: 250px;
            max-height: 50vh;
            overflow-y: auto;
            pointer-events: auto;
        }

        .panel-title {
            font-size: 1rem;
            font-weight: bold;
            color: #2ecc71;
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .location-search {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #2ecc71;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .location-search::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .quick-locations {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .location-btn {
            padding: 0.5rem;
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid rgba(46, 204, 113, 0.5);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            text-align: center;
        }

        .location-btn:hover {
            background: rgba(46, 204, 113, 0.4);
            transform: translateY(-2px);
        }

        .current-location {
            background: rgba(0, 0, 0, 0.8);
            padding: 0.8rem;
            border-radius: 8px;
            border: 1px solid rgba(46, 204, 113, 0.3);
            margin-top: 0.8rem;
        }

        .location-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.3rem;
            font-size: 0.8rem;
        }

        .osm-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #2ecc71;
            pointer-events: auto;
        }

        .control-btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid rgba(46, 204, 113, 0.5);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .control-btn:hover {
            background: rgba(46, 204, 113, 0.4);
        }

        .control-btn:last-child {
            margin-bottom: 0;
        }

        .minimap-container {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 250px;
            height: 200px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #2ecc71;
            border-radius: 10px;
            overflow: hidden;
            pointer-events: auto;
            z-index: 10;
        }

        #miniMap {
            width: 100%;
            height: 100%;
            background: #87CEEB;
        }

        /* Ensure Leaflet map displays properly */
        .leaflet-container {
            background: #87CEEB !important;
        }

        .leaflet-tile-pane {
            opacity: 1 !important;
        }

        .speed-hud {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #2ecc71;
            text-align: center;
        }

        .speed-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2ecc71;
            margin-bottom: 0.5rem;
        }

        .speed-unit {
            font-size: 1rem;
            opacity: 0.8;
        }

        .coordinates {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            opacity: 0.7;
        }

        .loading-osm {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 50%, #16a085 100%);
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
            overflow-y: auto;
            padding: 2rem;
            box-sizing: border-box;
        }

        .loading-title {
            font-size: 3rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .loading-progress {
            width: 400px;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 2rem 0;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f0f0f0);
            border-radius: 4px;
            animation: loading 3s ease-in-out;
        }

        @keyframes loading {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .osm-notice {
            background: rgba(46, 204, 113, 0.2);
            border: 2px solid #2ecc71;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
            text-align: center;
            max-width: 600px;
        }

        .start-btn {
            padding: 1rem 2rem;
            background: #2ecc71;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 1rem;
        }

        .start-btn:hover {
            background: #27ae60;
            transform: scale(1.05);
        }

        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #2ecc71;
            text-align: center;
            z-index: 200;
            animation: fadeInOut 4s ease-in-out;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .data-info {
            background: rgba(52, 152, 219, 0.2);
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }

        .feature-list {
            text-align: left;
            margin: 1rem 0;
        }

        .feature-list li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }

        .osm-logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading" class="loading-osm">
            <div class="osm-logo">🗺️</div>
            <div class="loading-title">OPENSTREETMAP DRIVING</div>
            <p style="font-size: 1.2rem; margin-bottom: 1rem;">Loading Real-World Open Data...</p>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>

            <div class="osm-notice">
                <h3>🌍 OpenStreetMap Integration</h3>
                <p><strong>Free & Open Source mapping data from around the world!</strong></p>

                <div class="data-info">
                    <h4>✨ Key Features:</h4>
                    <ul class="feature-list">
                        <li>🆓 <strong>Completely Free</strong> - No API keys or limits</li>
                        <li>🌐 <strong>Global Coverage</strong> - Drive anywhere in the world</li>
                        <li>🛣️ <strong>Real Roads</strong> - Actual road networks worldwide</li>
                        <li>🏢 <strong>Real Buildings</strong> - Actual building footprints</li>
                        <li>📍 <strong>GPS Accuracy</strong> - Real coordinates</li>
                        <li>🗺️ <strong>Live Minimap</strong> - Interactive OpenStreetMap</li>
                    </ul>
                </div>

                <p style="margin-top: 1.5rem; font-size: 1.2rem;">
                    <strong>🚀 Ready to explore the world?</strong>
                </p>
                <button class="start-btn" onclick="startOSMDriving()">🗺️ Start OpenStreetMap Driving</button>

                <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                    Powered by OpenStreetMap contributors worldwide 🌍
                </p>
            </div>
        </div>

        <div class="osm-hud" id="hud" style="display: none;">
            <!-- Location Panel -->
            <div class="location-panel">
                <div class="panel-title">🗺️ OpenStreetMap Navigation</div>

                <input type="text" class="location-search" id="locationSearch" placeholder="Search for any location...">

                <div class="quick-locations">
                    <button class="location-btn" onclick="goToLocation('New York, USA')">🏙️ New York</button>
                    <button class="location-btn" onclick="goToLocation('London, UK')">🇬🇧 London</button>
                    <button class="location-btn" onclick="goToLocation('Tokyo, Japan')">🇯🇵 Tokyo</button>
                    <button class="location-btn" onclick="goToLocation('Paris, France')">🇫🇷 Paris</button>
                    <button class="location-btn" onclick="goToLocation('Berlin, Germany')">🇩🇪 Berlin</button>
                    <button class="location-btn" onclick="getCurrentLocation()">📍 My Location</button>
                </div>

                <div class="current-location">
                    <div class="location-info">
                        <span>Current:</span>
                        <span id="currentLocationName">Loading...</span>
                    </div>
                    <div class="location-info">
                        <span>Lat:</span>
                        <span id="currentLat">0.000</span>
                    </div>
                    <div class="location-info">
                        <span>Lng:</span>
                        <span id="currentLng">0.000</span>
                    </div>
                    <div class="location-info">
                        <span>Data:</span>
                        <span id="dataSource">OpenStreetMap</span>
                    </div>
                </div>
            </div>

            <!-- OSM Controls -->
            <div class="osm-controls">
                <button class="control-btn" onclick="switchMapLayer()">🗺️ Map Layer</button>
                <button class="control-btn" onclick="toggleBuildings()">🏢 Buildings</button>
                <button class="control-btn" onclick="switchCamera()">📷 Camera</button>
                <button class="control-btn" onclick="resetCar()">🔄 Reset</button>
                <button class="control-btn" onclick="downloadArea()">💾 Cache Area</button>
            </div>

            <!-- Speed HUD -->
            <div class="speed-hud">
                <div class="speed-value" id="speedValue">0</div>
                <div class="speed-unit">KM/H</div>
                <div class="coordinates">
                    <div>Lat: <span id="carLat">0.000</span></div>
                    <div>Lng: <span id="carLng">0.000</span></div>
                </div>
                <div style="font-size: 0.7rem; margin-top: 0.5rem; opacity: 0.6;">
                    OSM Data
                </div>
            </div>

            <!-- Minimap -->
            <div class="minimap-container">
                <div id="miniMap"></div>
            </div>
        </div>

        <canvas id="canvas"></canvas>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // OpenStreetMap Car Driving Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let cameraMode = 0;

        // OpenStreetMap Integration
        let map, miniMap;
        let currentLocation = { lat: 40.7128, lng: -74.0060 }; // Default: New York
        let mapLayer = 'standard';
        let buildingsEnabled = true;
        let carMarker;

        // Real-world coordinates
        let realWorldLat = currentLocation.lat;
        let realWorldLng = currentLocation.lng;

        // OSM Data
        let osmData = {
            roads: [],
            buildings: [],
            landmarks: [],
            amenities: []
        };

        const cameraModes = ['Third Person', 'First Person', 'Satellite View', 'Street View'];
        const mapLayers = ['standard', 'satellite', 'terrain', 'humanitarian'];

        // Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false
        };

        function startOSMDriving() {
            console.log('🗺️ Starting OpenStreetMap Car Driving...');

            // Hide loading screen
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('hud').style.display = 'block';
                showNotification('🗺️ OpenStreetMap Loaded! Free & Open Source!');
            }, 3000);

            // Initialize 3D scene
            init3DScene();

            // Initialize OpenStreetMap
            initOpenStreetMap();

            // Setup location search
            setupLocationSearch();

            // Load initial area data
            loadOSMData(currentLocation.lat, currentLocation.lng);

            // Start game loop
            animate();
        }

        function init3DScene() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 8, -15);

            // Create renderer
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('canvas'),
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            createCar();
            setupLighting();
            setupEventListeners();

            console.log('✅ 3D scene initialized for OpenStreetMap');
        }

        function initOpenStreetMap() {
            console.log('🗺️ Initializing OpenStreetMap...');

            // Wait a bit for the DOM to be ready
            setTimeout(() => {
                try {
                    // Initialize Leaflet minimap
                    miniMap = L.map('miniMap', {
                        center: [currentLocation.lat, currentLocation.lng],
                        zoom: 15,
                        zoomControl: false,
                        attributionControl: false,
                        preferCanvas: true
                    });

                    // Add OpenStreetMap tile layer immediately
                    const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors',
                        maxZoom: 19,
                        crossOrigin: true
                    });

                    osmLayer.addTo(miniMap);

                    // Add car marker
                    const carIcon = L.divIcon({
                        className: 'car-marker',
                        html: '<div style="background: #e74c3c; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.5);"></div>',
                        iconSize: [16, 16],
                        iconAnchor: [8, 8]
                    });

                    carMarker = L.marker([currentLocation.lat, currentLocation.lng], { icon: carIcon }).addTo(miniMap);

                    // Force map to refresh
                    setTimeout(() => {
                        miniMap.invalidateSize();
                        console.log('✅ OpenStreetMap loaded successfully');
                    }, 500);

                } catch (error) {
                    console.error('❌ Error initializing OpenStreetMap:', error);
                }
            }, 1000);
        }

        function updateMapLayer() {
            if (!miniMap) return;

            try {
                // Remove existing layers
                miniMap.eachLayer((layer) => {
                    if (layer instanceof L.TileLayer) {
                        miniMap.removeLayer(layer);
                    }
                });

                let tileLayer;
                switch(mapLayer) {
                    case 'standard':
                        tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '© OpenStreetMap contributors',
                            maxZoom: 19,
                            crossOrigin: true
                        });
                        break;
                    case 'satellite':
                        tileLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                            attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                            maxZoom: 19
                        });
                        break;
                    case 'terrain':
                        tileLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                            attribution: '© OpenTopoMap (CC-BY-SA)',
                            maxZoom: 17
                        });
                        break;
                    case 'humanitarian':
                        tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
                            attribution: '© OpenStreetMap contributors, Tiles style by Humanitarian OpenStreetMap Team',
                            maxZoom: 19
                        });
                        break;
                }

                if (tileLayer) {
                    tileLayer.addTo(miniMap);
                    console.log(`🗺️ Map layer switched to: ${mapLayer}`);
                }
            } catch (error) {
                console.error('❌ Error updating map layer:', error);
            }
        }

        function loadOSMData(lat, lng) {
            console.log(`🔄 Loading OSM data for ${lat}, ${lng}`);

            // Define bounding box (approximately 2km x 2km area)
            const bbox = {
                south: lat - 0.01,
                west: lng - 0.01,
                north: lat + 0.01,
                east: lng + 0.01
            };

            // Load different types of OSM data
            loadOSMRoads(bbox);
            loadOSMBuildings(bbox);
            loadOSMLandmarks(bbox);

            // Generate 3D environment from OSM data
            setTimeout(() => {
                generateEnvironmentFromOSM();
            }, 2000);
        }

        function loadOSMRoads(bbox) {
            // Overpass API query for roads
            const query = `
                [out:json][timeout:25];
                (
                  way["highway"~"^(motorway|trunk|primary|secondary|tertiary|residential|service)$"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
                );
                out geom;
            `;

            const overpassUrl = 'https://overpass-api.de/api/interpreter';

            fetch(overpassUrl, {
                method: 'POST',
                body: query,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                osmData.roads = data.elements || [];
                console.log(`📍 Loaded ${osmData.roads.length} roads from OSM`);
            })
            .catch(error => {
                console.log('⚠️ Using simulated road data (OSM API unavailable)');
                osmData.roads = generateSimulatedRoads(bbox);
            });
        }

        function loadOSMBuildings(bbox) {
            // Overpass API query for buildings
            const query = `
                [out:json][timeout:25];
                (
                  way["building"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
                  relation["building"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
                );
                out geom;
            `;

            const overpassUrl = 'https://overpass-api.de/api/interpreter';

            fetch(overpassUrl, {
                method: 'POST',
                body: query,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                osmData.buildings = data.elements || [];
                console.log(`🏢 Loaded ${osmData.buildings.length} buildings from OSM`);
            })
            .catch(error => {
                console.log('⚠️ Using simulated building data (OSM API unavailable)');
                osmData.buildings = generateSimulatedBuildings(bbox);
            });
        }

        function loadOSMLandmarks(bbox) {
            // Overpass API query for landmarks and amenities
            const query = `
                [out:json][timeout:25];
                (
                  node["amenity"~"^(restaurant|cafe|hospital|school|bank|fuel|parking)$"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
                  node["tourism"~"^(attraction|museum|monument)$"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
                  way["leisure"~"^(park|playground|sports_centre)$"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
                );
                out geom;
            `;

            const overpassUrl = 'https://overpass-api.de/api/interpreter';

            fetch(overpassUrl, {
                method: 'POST',
                body: query,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                osmData.landmarks = data.elements || [];
                console.log(`🏛️ Loaded ${osmData.landmarks.length} landmarks from OSM`);
            })
            .catch(error => {
                console.log('⚠️ Using simulated landmark data (OSM API unavailable)');
                osmData.landmarks = generateSimulatedLandmarks(bbox);
            });
        }

        function generateSimulatedRoads(bbox) {
            // Generate realistic road network when OSM API is unavailable
            const roads = [];
            const centerLat = (bbox.north + bbox.south) / 2;
            const centerLng = (bbox.east + bbox.west) / 2;

            // Main roads
            for (let i = 0; i < 8; i++) {
                const angle = (i / 8) * Math.PI * 2;
                const length = 0.008; // ~800m

                roads.push({
                    type: 'way',
                    tags: { highway: i < 4 ? 'primary' : 'secondary' },
                    geometry: [
                        { lat: centerLat, lon: centerLng },
                        {
                            lat: centerLat + Math.sin(angle) * length,
                            lon: centerLng + Math.cos(angle) * length
                        }
                    ]
                });
            }

            // Grid roads
            for (let i = -3; i <= 3; i++) {
                for (let j = -3; j <= 3; j++) {
                    if (i === 0 && j === 0) continue;

                    const lat = centerLat + i * 0.003;
                    const lng = centerLng + j * 0.003;

                    roads.push({
                        type: 'way',
                        tags: { highway: 'residential' },
                        geometry: [
                            { lat: lat - 0.001, lon: lng },
                            { lat: lat + 0.001, lon: lng }
                        ]
                    });
                }
            }

            return roads;
        }

        function generateSimulatedBuildings(bbox) {
            // Generate realistic building distribution
            const buildings = [];
            const centerLat = (bbox.north + bbox.south) / 2;
            const centerLng = (bbox.east + bbox.west) / 2;

            for (let i = 0; i < 50; i++) {
                const lat = centerLat + (Math.random() - 0.5) * 0.015;
                const lng = centerLng + (Math.random() - 0.5) * 0.015;
                const size = 0.0001 + Math.random() * 0.0003;

                buildings.push({
                    type: 'way',
                    tags: {
                        building: 'yes',
                        'building:levels': Math.floor(1 + Math.random() * 10).toString()
                    },
                    geometry: [
                        { lat: lat - size, lon: lng - size },
                        { lat: lat - size, lon: lng + size },
                        { lat: lat + size, lon: lng + size },
                        { lat: lat + size, lon: lng - size },
                        { lat: lat - size, lon: lng - size }
                    ]
                });
            }

            return buildings;
        }

        function generateSimulatedLandmarks(bbox) {
            // Generate some landmarks
            const landmarks = [];
            const centerLat = (bbox.north + bbox.south) / 2;
            const centerLng = (bbox.east + bbox.west) / 2;

            const landmarkTypes = [
                { amenity: 'hospital', name: 'City Hospital' },
                { amenity: 'school', name: 'Local School' },
                { amenity: 'restaurant', name: 'Restaurant' },
                { leisure: 'park', name: 'City Park' },
                { tourism: 'attraction', name: 'Local Attraction' }
            ];

            landmarkTypes.forEach((type, index) => {
                const angle = (index / landmarkTypes.length) * Math.PI * 2;
                const distance = 0.005;

                landmarks.push({
                    type: 'node',
                    tags: { ...type },
                    lat: centerLat + Math.sin(angle) * distance,
                    lon: centerLng + Math.cos(angle) * distance
                });
            });

            return landmarks;
        }

        function generateEnvironmentFromOSM() {
            console.log('🏗️ Generating 3D environment from OSM data...');

            // Clear existing environment
            clearEnvironment();

            // Create ground
            createGround();

            // Generate roads from OSM data
            generateRoadsFromOSM();

            // Generate buildings from OSM data
            if (buildingsEnabled) {
                generateBuildingsFromOSM();
            }

            // Generate landmarks
            generateLandmarksFromOSM();

            console.log('✅ 3D environment generated from OpenStreetMap data');
            showNotification('🏗️ Environment built from real OSM data!');
        }

        function clearEnvironment() {
            // Remove all objects except car
            const objectsToRemove = [];
            scene.traverse((object) => {
                if (object !== car && object.type === 'Mesh' && object.parent === scene) {
                    objectsToRemove.push(object);
                }
            });

            objectsToRemove.forEach(object => {
                scene.remove(object);
            });
        }

        function createGround() {
            const groundGeometry = new THREE.PlaneGeometry(1000, 1000);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
        }

        function generateRoadsFromOSM() {
            const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const roadMarkingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            osmData.roads.forEach(road => {
                if (!road.geometry || road.geometry.length < 2) return;

                const roadWidth = getRoadWidth(road.tags.highway);

                for (let i = 0; i < road.geometry.length - 1; i++) {
                    const start = latLngTo3D(road.geometry[i].lat, road.geometry[i].lon);
                    const end = latLngTo3D(road.geometry[i + 1].lat, road.geometry[i + 1].lon);

                    const length = Math.sqrt(
                        Math.pow(end.x - start.x, 2) + Math.pow(end.z - start.z, 2)
                    );

                    if (length < 0.1) continue; // Skip very short segments

                    // Create road segment
                    const roadGeometry = new THREE.PlaneGeometry(length, roadWidth);
                    const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);

                    // Position and rotate road
                    roadMesh.position.set(
                        (start.x + end.x) / 2,
                        0.01,
                        (start.z + end.z) / 2
                    );

                    const angle = Math.atan2(end.z - start.z, end.x - start.x);
                    roadMesh.rotation.x = -Math.PI / 2;
                    roadMesh.rotation.z = angle;
                    roadMesh.receiveShadow = true;
                    scene.add(roadMesh);

                    // Add road markings for major roads
                    if (['motorway', 'trunk', 'primary'].includes(road.tags.highway)) {
                        const markingGeometry = new THREE.PlaneGeometry(length * 0.8, 0.3);
                        const marking = new THREE.Mesh(markingGeometry, roadMarkingMaterial);
                        marking.position.copy(roadMesh.position);
                        marking.position.y += 0.001;
                        marking.rotation.copy(roadMesh.rotation);
                        scene.add(marking);
                    }
                }
            });
        }

        function getRoadWidth(highway) {
            const widths = {
                'motorway': 20,
                'trunk': 15,
                'primary': 12,
                'secondary': 10,
                'tertiary': 8,
                'residential': 6,
                'service': 4
            };
            return widths[highway] || 6;
        }

        function generateBuildingsFromOSM() {
            osmData.buildings.forEach(building => {
                if (!building.geometry || building.geometry.length < 3) return;

                // Calculate building center and size
                const bounds = getBuildingBounds(building.geometry);
                const center = latLngTo3D(bounds.centerLat, bounds.centerLng);
                const width = Math.max(bounds.width * 111320, 5); // Convert to meters, min 5m
                const depth = Math.max(bounds.height * 111320, 5);

                // Get building height
                const levels = parseInt(building.tags['building:levels']) || Math.floor(1 + Math.random() * 8);
                const height = levels * 3.5; // 3.5m per floor

                // Create building
                const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
                const buildingMaterial = new THREE.MeshLambertMaterial({
                    color: getBuildingColor(building.tags.building)
                });
                const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);

                buildingMesh.position.set(center.x, height / 2, center.z);
                buildingMesh.castShadow = true;
                buildingMesh.receiveShadow = true;
                scene.add(buildingMesh);

                // Add windows
                addBuildingWindows(buildingMesh, width, height, depth, levels);
            });
        }

        function getBuildingBounds(geometry) {
            let minLat = Infinity, maxLat = -Infinity;
            let minLng = Infinity, maxLng = -Infinity;

            geometry.forEach(point => {
                minLat = Math.min(minLat, point.lat);
                maxLat = Math.max(maxLat, point.lat);
                minLng = Math.min(minLng, point.lon);
                maxLng = Math.max(maxLng, point.lon);
            });

            return {
                centerLat: (minLat + maxLat) / 2,
                centerLng: (minLng + maxLng) / 2,
                width: maxLng - minLng,
                height: maxLat - minLat
            };
        }

        function getBuildingColor(buildingType) {
            const colors = {
                'residential': 0x8B4513,
                'commercial': 0x4682B4,
                'industrial': 0x696969,
                'office': 0x2F4F4F,
                'retail': 0x800000,
                'hospital': 0xFFFFFF,
                'school': 0xDDA0DD,
                'church': 0x8B7355
            };
            return colors[buildingType] || 0x708090;
        }

        function addBuildingWindows(building, width, height, depth, levels) {
            const windowMaterial = new THREE.MeshBasicMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });

            const windowsPerFloor = Math.max(2, Math.floor(width / 4));

            for (let floor = 1; floor < levels; floor++) {
                for (let window = 0; window < windowsPerFloor; window++) {
                    // Front face windows
                    const windowGeometry = new THREE.PlaneGeometry(1.5, 2);
                    const windowMesh = new THREE.Mesh(windowGeometry, windowMaterial);

                    windowMesh.position.set(
                        -width/2 + (window + 0.5) * (width / windowsPerFloor),
                        -height/2 + floor * (height / levels),
                        depth/2 + 0.01
                    );

                    building.add(windowMesh);
                }
            }
        }

        function generateLandmarksFromOSM() {
            osmData.landmarks.forEach(landmark => {
                const position = latLngTo3D(landmark.lat, landmark.lon);

                if (landmark.tags.leisure === 'park') {
                    createPark(position.x, position.z);
                } else if (landmark.tags.amenity === 'hospital') {
                    createHospital(position.x, position.z);
                } else if (landmark.tags.amenity === 'school') {
                    createSchool(position.x, position.z);
                } else if (landmark.tags.tourism) {
                    createTourismSite(position.x, position.z, landmark.tags.tourism);
                }
            });
        }

        function latLngTo3D(lat, lng) {
            // Convert lat/lng to 3D coordinates relative to current location
            const scale = 111320; // Approximate meters per degree
            const x = (lng - realWorldLng) * scale * Math.cos(realWorldLat * Math.PI / 180);
            const z = -(lat - realWorldLat) * scale; // Negative because Z is inverted
            return { x, z };
        }

        function createPark(x, z) {
            // Park ground
            const parkGeometry = new THREE.PlaneGeometry(30, 30);
            const parkMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const park = new THREE.Mesh(parkGeometry, parkMaterial);
            park.rotation.x = -Math.PI / 2;
            park.position.set(x, 0.02, z);
            scene.add(park);

            // Trees
            for (let i = 0; i < 8; i++) {
                const treeX = x + (Math.random() - 0.5) * 25;
                const treeZ = z + (Math.random() - 0.5) * 25;
                createTree(treeX, treeZ);
            }
        }

        function createTree(x, z) {
            // Trunk
            const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 6);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(x, 3, z);
            trunk.castShadow = true;
            scene.add(trunk);

            // Leaves
            const leavesGeometry = new THREE.SphereGeometry(3, 12, 12);
            const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
            leaves.position.set(x, 8, z);
            leaves.castShadow = true;
            scene.add(leaves);
        }

        function createHospital(x, z) {
            // Hospital building
            const hospitalGeometry = new THREE.BoxGeometry(40, 20, 30);
            const hospitalMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
            const hospital = new THREE.Mesh(hospitalGeometry, hospitalMaterial);
            hospital.position.set(x, 10, z);
            hospital.castShadow = true;
            scene.add(hospital);

            // Red cross
            const crossGeometry = new THREE.BoxGeometry(2, 8, 1);
            const crossMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });
            const cross1 = new THREE.Mesh(crossGeometry, crossMaterial);
            cross1.position.set(x, 15, z + 15.1);
            scene.add(cross1);

            const cross2 = new THREE.Mesh(new THREE.BoxGeometry(8, 2, 1), crossMaterial);
            cross2.position.set(x, 15, z + 15.1);
            scene.add(cross2);
        }

        function createSchool(x, z) {
            // School building
            const schoolGeometry = new THREE.BoxGeometry(35, 12, 25);
            const schoolMaterial = new THREE.MeshLambertMaterial({ color: 0xDDA0DD });
            const school = new THREE.Mesh(schoolGeometry, schoolMaterial);
            school.position.set(x, 6, z);
            school.castShadow = true;
            scene.add(school);

            // Playground
            const playgroundGeometry = new THREE.PlaneGeometry(20, 20);
            const playgroundMaterial = new THREE.MeshLambertMaterial({ color: 0x8B7355 });
            const playground = new THREE.Mesh(playgroundGeometry, playgroundMaterial);
            playground.rotation.x = -Math.PI / 2;
            playground.position.set(x + 30, 0.01, z);
            scene.add(playground);
        }

        function createTourismSite(x, z, type) {
            let color = 0x8B7355;
            let height = 15;

            if (type === 'monument') {
                color = 0x696969;
                height = 25;
            } else if (type === 'museum') {
                color = 0x4682B4;
                height = 12;
            }

            const siteGeometry = new THREE.BoxGeometry(20, height, 20);
            const siteMaterial = new THREE.MeshLambertMaterial({ color: color });
            const site = new THREE.Mesh(siteGeometry, siteMaterial);
            site.position.set(x, height / 2, z);
            site.castShadow = true;
            scene.add(site);
        }

        function createCar() {
            // Car body
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshLambertMaterial({ color: 0xe74c3c });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.castShadow = true;
            scene.add(car);

            // Add car details
            addCarDetails();

            console.log('🚗 Car created for OpenStreetMap driving');
        }

        function addCarDetails() {
            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(1.8, 0.8, 0.1);
            const windshieldMaterial = new THREE.MeshLambertMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.2, 1.5);
            car.add(windshield);

            // Wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 8);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            const wheelPositions = [
                { x: -1, y: -0.3, z: 1.2 },
                { x: 1, y: -0.3, z: 1.2 },
                { x: -1, y: -0.3, z: -1.2 },
                { x: 1, y: -0.3, z: -1.2 }
            ];

            car.wheels = [];
            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                car.add(wheel);
                car.wheels.push(wheel);
            });
        }

        function setupLighting() {
            // Ambient light
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // Directional light (sun)
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 25);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -200;
            directionalLight.shadow.camera.right = 200;
            directionalLight.shadow.camera.top = 200;
            directionalLight.shadow.camera.bottom = -200;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 300;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
        }

        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }

                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }
            });

            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function setupLocationSearch() {
            const searchInput = document.getElementById('locationSearch');

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const location = e.target.value;
                    goToLocation(location);
                }
            });
        }

        function goToLocation(locationName) {
            console.log(`🔍 Searching for: ${locationName}`);

            // Use Nominatim API for geocoding (OpenStreetMap's geocoding service)
            const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(locationName)}&limit=1`;

            fetch(nominatimUrl)
                .then(response => response.json())
                .then(data => {
                    if (data && data.length > 0) {
                        const result = data[0];
                        currentLocation = {
                            lat: parseFloat(result.lat),
                            lng: parseFloat(result.lon)
                        };

                        realWorldLat = currentLocation.lat;
                        realWorldLng = currentLocation.lng;

                        // Update UI
                        updateLocationDisplay(result.display_name);

                        // Update minimap
                        miniMap.setView([currentLocation.lat, currentLocation.lng], 15);
                        carMarker.setLatLng([currentLocation.lat, currentLocation.lng]);

                        // Load new OSM data
                        loadOSMData(currentLocation.lat, currentLocation.lng);

                        showNotification(`📍 Moved to ${locationName}`);
                    } else {
                        showNotification('❌ Location not found. Try a different search.');
                    }
                })
                .catch(error => {
                    console.error('Geocoding error:', error);
                    showNotification('❌ Search failed. Check your connection.');
                });
        }

        function getCurrentLocation() {
            if (!navigator.geolocation) {
                showNotification('❌ Geolocation not supported by this browser.');
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    realWorldLat = currentLocation.lat;
                    realWorldLng = currentLocation.lng;

                    // Get location name using reverse geocoding
                    const nominatimUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${currentLocation.lat}&lon=${currentLocation.lng}`;

                    fetch(nominatimUrl)
                        .then(response => response.json())
                        .then(data => {
                            if (data && data.display_name) {
                                updateLocationDisplay(data.display_name);
                            }
                        })
                        .catch(error => {
                            console.log('Reverse geocoding failed:', error);
                        });

                    // Update minimap
                    miniMap.setView([currentLocation.lat, currentLocation.lng], 15);
                    carMarker.setLatLng([currentLocation.lat, currentLocation.lng]);

                    // Load new OSM data
                    loadOSMData(currentLocation.lat, currentLocation.lng);

                    showNotification('📍 Using your current location');
                },
                (error) => {
                    showNotification('❌ Unable to get your location. Using default.');
                }
            );
        }

        function updateLocationDisplay(locationName) {
            document.getElementById('currentLocationName').textContent = locationName;
            document.getElementById('currentLat').textContent = realWorldLat.toFixed(6);
            document.getElementById('currentLng').textContent = realWorldLng.toFixed(6);
        }

        function switchMapLayer() {
            const currentIndex = mapLayers.indexOf(mapLayer);
            mapLayer = mapLayers[(currentIndex + 1) % mapLayers.length];
            updateMapLayer();
            showNotification(`🗺️ Map layer: ${mapLayer}`);
        }

        function toggleBuildings() {
            buildingsEnabled = !buildingsEnabled;
            generateEnvironmentFromOSM();
            showNotification(buildingsEnabled ? '🏢 Buildings ON' : '🏢 Buildings OFF');
        }

        function downloadArea() {
            // Simulate downloading area for offline use
            showNotification('💾 Area cached for offline use!');
        }

        function updateCar() {
            const acceleration = 0.03;
            const maxSpeed = 0.8;
            const friction = 0.96;
            const turnSpeed = 0.04;

            let engineForce = 0;
            let steerAngle = 0;

            if (keys.w || keys.ArrowUp) engineForce = acceleration;
            if (keys.s || keys.ArrowDown) engineForce = -acceleration * 0.5;
            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;

            if (Math.abs(engineForce) > 0.001) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;
            }

            carVelocity.x *= friction;
            carVelocity.z *= friction;

            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }

            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.01) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }

            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;

            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;

            // Animate wheels
            if (car.wheels) {
                const wheelRotation = currentSpeed * 5;
                car.wheels.forEach((wheel, index) => {
                    wheel.rotation.x += wheelRotation;
                    if (index < 2) {
                        wheel.rotation.y = steerAngle * 3;
                    }
                });
            }

            speed = currentSpeed * 120;

            // Update real-world coordinates
            const metersPerUnit = 10;
            const deltaLat = (carVelocity.z * metersPerUnit) / 111320;
            const deltaLng = (carVelocity.x * metersPerUnit) / (111320 * Math.cos(realWorldLat * Math.PI / 180));

            realWorldLat += deltaLat;
            realWorldLng += deltaLng;

            // Update minimap car position
            if (carMarker) {
                const newPosition = [realWorldLat, realWorldLng];
                carMarker.setLatLng(newPosition);
                miniMap.setView(newPosition, miniMap.getZoom());
            }
        }

        function updateCamera() {
            const smoothness = 0.1;
            let targetX, targetY, targetZ;

            switch(cameraMode) {
                case 0: // Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 12;
                    targetY = carPosition.y + 6;
                    targetZ = carPosition.z - Math.cos(carRotation) * 12;
                    break;
                case 1: // First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 0.5;
                    targetY = carPosition.y + 1.5;
                    targetZ = carPosition.z + Math.cos(carRotation) * 0.5;
                    break;
                case 2: // Satellite View
                    targetX = carPosition.x;
                    targetY = carPosition.y + 50;
                    targetZ = carPosition.z;
                    break;
                case 3: // Street View
                    targetX = carPosition.x - Math.sin(carRotation) * 8;
                    targetY = carPosition.y + 3;
                    targetZ = carPosition.z - Math.cos(carRotation) * 8;
                    break;
            }

            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(carPosition.x, carPosition.y, carPosition.z);
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            showNotification(`📷 ${cameraModes[cameraMode]}`);
        }

        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            showNotification('🔄 Car reset');
        }

        function updateUI() {
            document.getElementById('speedValue').textContent = Math.round(speed);
            document.getElementById('carLat').textContent = realWorldLat.toFixed(6);
            document.getElementById('carLng').textContent = realWorldLng.toFixed(6);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.getElementById('gameContainer').appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        function animate() {
            requestAnimationFrame(animate);

            updateCar();
            updateCamera();
            updateUI();

            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
