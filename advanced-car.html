<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Car Driving Simulator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Courier New', monospace;
            color: white;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        canvas {
            display: block;
        }

        .advanced-hud {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .speedometer-container {
            position: absolute;
            bottom: 30px;
            right: 30px;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 100%);
            border: 3px solid #00f5ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .speed-needle {
            position: absolute;
            width: 2px;
            height: 80px;
            background: #ff4444;
            transform-origin: bottom center;
            transition: transform 0.1s ease;
            border-radius: 2px;
            box-shadow: 0 0 10px #ff4444;
        }

        .speed-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            z-index: 1;
        }

        .speed-unit {
            font-size: 1rem;
            color: #ccc;
            z-index: 1;
        }

        .dashboard {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            min-width: 300px;
            backdrop-filter: blur(10px);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .dashboard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: rgba(0, 245, 255, 0.1);
            border-radius: 5px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .dashboard-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .dashboard-value {
            font-weight: bold;
            color: #00f5ff;
        }

        .performance-bars {
            margin-top: 1rem;
        }

        .performance-bar {
            margin-bottom: 0.5rem;
        }

        .performance-label {
            font-size: 0.8rem;
            margin-bottom: 0.2rem;
            opacity: 0.8;
        }

        .bar-container {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .bar-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .bar-engine { background: linear-gradient(90deg, #ff4444, #ff8844); }
        .bar-brake { background: linear-gradient(90deg, #44ff44, #88ff44); }
        .bar-turbo { background: linear-gradient(90deg, #4444ff, #8844ff); }

        .weather-system {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            text-align: center;
            min-width: 150px;
        }

        .weather-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .time-display {
            position: absolute;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #ffaa00;
            text-align: center;
        }

        .time-value {
            font-size: 1.5rem;
            color: #ffaa00;
            font-weight: bold;
        }

        .achievements {
            position: absolute;
            bottom: 30px;
            left: 30px;
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #44ff44;
            max-width: 250px;
        }

        .achievement-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            background: rgba(68, 255, 68, 0.1);
            border-radius: 5px;
        }

        .achievement-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .controls-advanced {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem 2rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            text-align: center;
            font-size: 0.9rem;
        }

        .particle-effect {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00f5ff;
            border-radius: 50%;
            pointer-events: none;
            animation: sparkle 1s linear infinite;
        }

        @keyframes sparkle {
            0% { opacity: 1; transform: scale(1) translateY(0); }
            100% { opacity: 0; transform: scale(0) translateY(-30px); }
        }

        .notification-advanced {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            text-align: center;
            font-size: 1.2rem;
            z-index: 200;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .loading-advanced {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
        }

        .loading-title {
            font-size: 3rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #00f5ff, #ff4444, #44ff44);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .loading-progress-advanced {
            width: 400px;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 2rem 0;
        }

        .loading-bar-advanced {
            height: 100%;
            background: linear-gradient(90deg, #00f5ff, #ff4444);
            border-radius: 4px;
            animation: loadingAdvanced 3s ease-in-out;
        }

        @keyframes loadingAdvanced {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .feature-list {
            text-align: left;
            margin-top: 2rem;
            opacity: 0.8;
        }

        .feature-item {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading" class="loading-advanced">
            <div class="loading-title">🏎️ ADVANCED CAR SIMULATOR</div>
            <p style="font-size: 1.2rem; margin-bottom: 1rem;">Loading Advanced Features...</p>
            <div class="loading-progress-advanced">
                <div class="loading-bar-advanced"></div>
            </div>
            <div class="feature-list">
                <div class="feature-item">✅ Realistic Physics Engine</div>
                <div class="feature-item">✅ Dynamic Weather System</div>
                <div class="feature-item">✅ Achievement System</div>
                <div class="feature-item">✅ Advanced Dashboard</div>
                <div class="feature-item">✅ Particle Effects</div>
                <div class="feature-item">✅ Day/Night Cycle</div>
            </div>
        </div>

        <div class="advanced-hud" id="hud" style="display: none;">
            <!-- Dashboard -->
            <div class="dashboard">
                <h3 style="margin: 0 0 1rem 0; color: #00f5ff; text-align: center;">🚗 Vehicle Dashboard</h3>
                <div class="dashboard-grid">
                    <div class="dashboard-item">
                        <span class="dashboard-label">Speed</span>
                        <span class="dashboard-value" id="dashSpeed">0 km/h</span>
                    </div>
                    <div class="dashboard-item">
                        <span class="dashboard-label">RPM</span>
                        <span class="dashboard-value" id="dashRPM">0</span>
                    </div>
                    <div class="dashboard-item">
                        <span class="dashboard-label">Gear</span>
                        <span class="dashboard-value" id="dashGear">P</span>
                    </div>
                    <div class="dashboard-item">
                        <span class="dashboard-label">Fuel</span>
                        <span class="dashboard-value" id="dashFuel">100%</span>
                    </div>
                    <div class="dashboard-item">
                        <span class="dashboard-label">Distance</span>
                        <span class="dashboard-value" id="dashDistance">0 m</span>
                    </div>
                    <div class="dashboard-item">
                        <span class="dashboard-label">Camera</span>
                        <span class="dashboard-value" id="dashCamera">Third Person</span>
                    </div>
                </div>

                <div class="performance-bars">
                    <div class="performance-bar">
                        <div class="performance-label">Engine Power</div>
                        <div class="bar-container">
                            <div class="bar-fill bar-engine" id="engineBar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="performance-bar">
                        <div class="performance-label">Brake Force</div>
                        <div class="bar-container">
                            <div class="bar-fill bar-brake" id="brakeBar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="performance-bar">
                        <div class="performance-label">Turbo Boost</div>
                        <div class="bar-container">
                            <div class="bar-fill bar-turbo" id="turboBar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Speedometer -->
            <div class="speedometer-container">
                <div class="speed-needle" id="speedNeedle"></div>
                <div class="speed-value" id="speedValue">0</div>
                <div class="speed-unit">KM/H</div>
            </div>

            <!-- Weather System -->
            <div class="weather-system">
                <div class="weather-icon" id="weatherIcon">☀️</div>
                <div id="weatherText">Sunny</div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem;">
                    <span id="temperature">22°C</span>
                </div>
            </div>

            <!-- Time Display -->
            <div class="time-display">
                <div style="font-size: 0.9rem; margin-bottom: 0.5rem;">Game Time</div>
                <div class="time-value" id="gameTime">12:00</div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem;" id="dayPhase">Day</div>
            </div>

            <!-- Achievements -->
            <div class="achievements">
                <h4 style="margin: 0 0 1rem 0; color: #44ff44;">🏆 Achievements</h4>
                <div id="achievementsList">
                    <div class="achievement-item">
                        <span class="achievement-icon">🚗</span>
                        <span>First Drive</span>
                    </div>
                </div>
            </div>

            <!-- Controls -->
            <div class="controls-advanced">
                <strong>🎮 Advanced Controls:</strong><br>
                WASD: Drive • C: Camera • R: Reset • T: Turbo • B: Brake • L: Lights • N: Night Mode
            </div>
        </div>

        <canvas id="canvas"></canvas>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Advanced Game Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let rpm = 0;
        let fuel = 100;
        let distance = 0;
        let cameraMode = 0;
        let gameTime = 12.0; // 12:00 PM
        let isNightMode = false;
        let turboActive = false;
        let lightsOn = false;
        let enginePower = 0;
        let brakeForce = 0;
        let turboCharge = 100;

        const cameraModes = ['Third Person', 'First Person', 'Top Down', 'Cinematic', 'Hood View'];
        const weatherStates = [
            { icon: '☀️', text: 'Sunny', temp: 22 },
            { icon: '⛅', text: 'Cloudy', temp: 18 },
            { icon: '🌧️', text: 'Rainy', temp: 15 },
            { icon: '❄️', text: 'Snowy', temp: -2 },
            { icon: '🌫️', text: 'Foggy', temp: 12 }
        ];
        let currentWeather = 0;

        // Achievements system
        let achievements = [
            { id: 'firstDrive', name: 'First Drive', icon: '🚗', unlocked: true },
            { id: 'speedDemon', name: 'Speed Demon', icon: '💨', unlocked: false, requirement: 'Reach 100 km/h' },
            { id: 'longDistance', name: 'Long Distance', icon: '🛣️', unlocked: false, requirement: 'Drive 1000m' },
            { id: 'nightRider', name: 'Night Rider', icon: '🌙', unlocked: false, requirement: 'Drive at night' },
            { id: 'weatherMaster', name: 'Weather Master', icon: '🌦️', unlocked: false, requirement: 'Drive in all weather' }
        ];

        // Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false,
            t: false, b: false, l: false, n: false
        };

        function init() {
            console.log('🏎️ Initializing Advanced Car Simulator...');

            // Hide loading screen after delay
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('hud').style.display = 'block';
                showNotification('🏎️ Advanced Car Simulator Ready!');
            }, 3000);

            // Create scene
            scene = new THREE.Scene();
            updateSceneBackground();

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

            // Create renderer with advanced settings
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('canvas'),
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Create environment
            createAdvancedEnvironment();
            createAdvancedCar();
            setupAdvancedLighting();
            setupEventListeners();

            // Start game loop
            animate();

            console.log('✅ Advanced simulator initialized!');
        }

        function createAdvancedEnvironment() {
            // Ground with texture-like appearance
            const groundGeometry = new THREE.PlaneGeometry(500, 500);
            const groundMaterial = new THREE.MeshLambertMaterial({
                color: isNightMode ? 0x2a3d23 : 0x4a5d23
            });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Advanced road network
            createAdvancedRoads();

            // City environment
            createCityEnvironment();

            // Dynamic elements
            createDynamicElements();
        }

        function createAdvancedRoads() {
            // Main highway
            const mainRoadGeometry = new THREE.PlaneGeometry(500, 25);
            const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const mainRoad = new THREE.Mesh(mainRoadGeometry, roadMaterial);
            mainRoad.rotation.x = -Math.PI / 2;
            mainRoad.position.y = 0.01;
            mainRoad.receiveShadow = true;
            scene.add(mainRoad);

            // Cross roads
            const crossRoadGeometry = new THREE.PlaneGeometry(25, 500);
            const crossRoad = new THREE.Mesh(crossRoadGeometry, roadMaterial);
            crossRoad.rotation.x = -Math.PI / 2;
            crossRoad.position.y = 0.01;
            crossRoad.receiveShadow = true;
            scene.add(crossRoad);

            // Road markings with better detail
            createDetailedRoadMarkings();
        }

        function createDetailedRoadMarkings() {
            const markingGeometry = new THREE.PlaneGeometry(6, 0.6);
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            // Center line markings
            for (let i = -250; i < 250; i += 20) {
                const marking = new THREE.Mesh(markingGeometry, markingMaterial);
                marking.rotation.x = -Math.PI / 2;
                marking.position.set(i, 0.02, 0);
                scene.add(marking);

                const crossMarking = new THREE.Mesh(markingGeometry, markingMaterial);
                crossMarking.rotation.x = -Math.PI / 2;
                crossMarking.rotation.z = Math.PI / 2;
                crossMarking.position.set(0, 0.02, i);
                scene.add(crossMarking);
            }
        }

        function createCityEnvironment() {
            // Skyscrapers
            const buildingConfigs = [
                { x: 60, z: 60, w: 20, h: 40, d: 15, color: 0x8B4513 },
                { x: -60, z: 60, w: 15, h: 35, d: 20, color: 0x696969 },
                { x: 80, z: -40, w: 25, h: 50, d: 18, color: 0x4682B4 },
                { x: -80, z: -40, w: 18, h: 45, d: 22, color: 0x8B0000 },
                { x: 100, z: 80, w: 30, h: 60, d: 25, color: 0x2F4F4F },
                { x: -100, z: 80, w: 22, h: 55, d: 20, color: 0x800080 },
                { x: 120, z: -60, w: 28, h: 65, d: 24, color: 0x4B0082 },
                { x: -120, z: -60, w: 26, h: 58, d: 22, color: 0x006400 }
            ];

            buildingConfigs.forEach(config => {
                const building = createDetailedBuilding(config);
                scene.add(building);
            });

            // Street furniture
            createStreetFurniture();
        }

        function createDetailedBuilding(config) {
            const buildingGeometry = new THREE.BoxGeometry(config.w, config.h, config.d);
            const buildingMaterial = new THREE.MeshLambertMaterial({ color: config.color });
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
            building.position.set(config.x, config.h / 2, config.z);
            building.castShadow = true;
            building.receiveShadow = true;

            // Add windows with lights
            addBuildingWindows(building, config);

            // Add rooftop details
            addRooftopDetails(building, config);

            return building;
        }

        function addBuildingWindows(building, config) {
            const windowGeometry = new THREE.PlaneGeometry(2, 2);
            const windowMaterial = new THREE.MeshBasicMaterial({
                color: isNightMode ? 0xffffaa : 0x87CEEB,
                transparent: true,
                opacity: isNightMode ? 0.8 : 0.6
            });

            // Front and back faces
            for (let i = -config.w/2 + 3; i < config.w/2; i += 4) {
                for (let j = 3; j < config.h - 3; j += 5) {
                    // Front face
                    const frontWindow = new THREE.Mesh(windowGeometry, windowMaterial);
                    frontWindow.position.set(i, j - config.h/2, config.d/2 + 0.1);
                    building.add(frontWindow);

                    // Back face
                    const backWindow = new THREE.Mesh(windowGeometry, windowMaterial);
                    backWindow.position.set(i, j - config.h/2, -config.d/2 - 0.1);
                    backWindow.rotation.y = Math.PI;
                    building.add(backWindow);
                }
            }
        }

        function addRooftopDetails(building, config) {
            // Antenna
            const antennaGeometry = new THREE.CylinderGeometry(0.1, 0.1, 5);
            const antennaMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
            const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
            antenna.position.set(0, config.h/2 + 2.5, 0);
            building.add(antenna);

            // Rooftop light
            if (isNightMode) {
                const lightGeometry = new THREE.SphereGeometry(0.5);
                const lightMaterial = new THREE.MeshBasicMaterial({
                    color: 0xff0000,
                    emissive: 0xff0000
                });
                const rooftopLight = new THREE.Mesh(lightGeometry, lightMaterial);
                rooftopLight.position.set(0, config.h/2 + 0.5, 0);
                building.add(rooftopLight);
            }
        }

        function createStreetFurniture() {
            // Street lights with advanced design
            const lightPositions = [
                { x: 20, z: 40 }, { x: -20, z: 40 }, { x: 20, z: -40 }, { x: -20, z: -40 },
                { x: 40, z: 20 }, { x: 40, z: -20 }, { x: -40, z: 20 }, { x: -40, z: -20 }
            ];

            lightPositions.forEach(pos => {
                createAdvancedStreetLight(pos.x, pos.z);
            });

            // Traffic signs
            createTrafficSigns();

            // Trees with seasonal variation
            createSeasonalTrees();
        }

        function createAdvancedStreetLight(x, z) {
            // Pole
            const poleGeometry = new THREE.CylinderGeometry(0.15, 0.15, 12);
            const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.set(x, 6, z);
            pole.castShadow = true;
            scene.add(pole);

            // Light fixture
            const fixtureGeometry = new THREE.CylinderGeometry(1, 0.8, 2);
            const fixtureMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
            fixture.position.set(x, 11, z);
            scene.add(fixture);

            // Light bulb
            const bulbGeometry = new THREE.SphereGeometry(0.6);
            const bulbMaterial = new THREE.MeshBasicMaterial({
                color: isNightMode ? 0xffffaa : 0xcccccc,
                emissive: isNightMode ? 0x444400 : 0x000000
            });
            const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial);
            bulb.position.set(x, 11, z);
            scene.add(bulb);

            // Point light for illumination
            if (isNightMode) {
                const pointLight = new THREE.PointLight(0xffffaa, 1, 50);
                pointLight.position.set(x, 11, z);
                pointLight.castShadow = true;
                scene.add(pointLight);
            }
        }

        function createTrafficSigns() {
            const signPositions = [
                { x: 15, z: 15, type: 'stop' },
                { x: -15, z: -15, type: 'yield' },
                { x: 25, z: -25, type: 'speed' }
            ];

            signPositions.forEach(pos => {
                createTrafficSign(pos.x, pos.z, pos.type);
            });
        }

        function createTrafficSign(x, z, type) {
            // Sign post
            const postGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3);
            const postMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
            const post = new THREE.Mesh(postGeometry, postMaterial);
            post.position.set(x, 1.5, z);
            scene.add(post);

            // Sign board
            const signGeometry = new THREE.PlaneGeometry(2, 2);
            let signColor = 0xff0000; // Default red
            if (type === 'yield') signColor = 0xffff00;
            if (type === 'speed') signColor = 0xffffff;

            const signMaterial = new THREE.MeshBasicMaterial({ color: signColor });
            const sign = new THREE.Mesh(signGeometry, signMaterial);
            sign.position.set(x, 3, z);
            scene.add(sign);
        }

        function createSeasonalTrees() {
            const treePositions = [
                { x: 35, z: 35 }, { x: -35, z: 35 }, { x: 35, z: -35 }, { x: -35, z: -35 },
                { x: 55, z: 25 }, { x: -55, z: 25 }, { x: 55, z: -25 }, { x: -55, z: -25 }
            ];

            treePositions.forEach(pos => {
                createDetailedTree(pos.x, pos.z);
            });
        }

        function createDetailedTree(x, z) {
            // Trunk with texture
            const trunkGeometry = new THREE.CylinderGeometry(0.4, 0.6, 8);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(x, 4, z);
            trunk.castShadow = true;
            scene.add(trunk);

            // Leaves with seasonal colors
            const leavesGeometry = new THREE.SphereGeometry(4, 12, 12);
            let leafColor = 0x228B22; // Default green
            if (currentWeather === 3) leafColor = 0x8B4513; // Brown for winter

            const leavesMaterial = new THREE.MeshLambertMaterial({ color: leafColor });
            const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
            leaves.position.set(x, 10, z);
            leaves.castShadow = true;
            scene.add(leaves);
        }

        function createDynamicElements() {
            // Animated elements like flags, moving objects
            createAnimatedFlags();
            createParticleSystem();
        }

        function createAnimatedFlags() {
            const flagPositions = [
                { x: 50, z: 0 }, { x: -50, z: 0 }
            ];

            flagPositions.forEach(pos => {
                // Flag pole
                const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 10);
                const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
                const pole = new THREE.Mesh(poleGeometry, poleMaterial);
                pole.position.set(pos.x, 5, pos.z);
                scene.add(pole);

                // Flag
                const flagGeometry = new THREE.PlaneGeometry(3, 2);
                const flagMaterial = new THREE.MeshBasicMaterial({
                    color: 0xff0000,
                    side: THREE.DoubleSide
                });
                const flag = new THREE.Mesh(flagGeometry, flagMaterial);
                flag.position.set(pos.x + 1.5, 8.5, pos.z);
                scene.add(flag);

                // Store reference for animation
                flag.userData = { originalX: pos.x + 1.5, time: 0 };
                scene.userData.flags = scene.userData.flags || [];
                scene.userData.flags.push(flag);
            });
        }

        function createParticleSystem() {
            // Weather particles
            scene.userData.particles = [];

            if (currentWeather === 2) { // Rain
                createRainParticles();
            } else if (currentWeather === 3) { // Snow
                createSnowParticles();
            }
        }

        function createRainParticles() {
            for (let i = 0; i < 100; i++) {
                const rainGeometry = new THREE.CylinderGeometry(0.02, 0.02, 2);
                const rainMaterial = new THREE.MeshBasicMaterial({
                    color: 0x87CEEB,
                    transparent: true,
                    opacity: 0.6
                });
                const rainDrop = new THREE.Mesh(rainGeometry, rainMaterial);

                rainDrop.position.set(
                    (Math.random() - 0.5) * 200,
                    Math.random() * 50 + 20,
                    (Math.random() - 0.5) * 200
                );

                rainDrop.userData = {
                    velocity: -0.5 - Math.random() * 0.5,
                    resetY: Math.random() * 50 + 20
                };

                scene.add(rainDrop);
                scene.userData.particles.push(rainDrop);
            }
        }

        function createSnowParticles() {
            for (let i = 0; i < 50; i++) {
                const snowGeometry = new THREE.SphereGeometry(0.1, 6, 6);
                const snowMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
                const snowFlake = new THREE.Mesh(snowGeometry, snowMaterial);

                snowFlake.position.set(
                    (Math.random() - 0.5) * 200,
                    Math.random() * 50 + 20,
                    (Math.random() - 0.5) * 200
                );

                snowFlake.userData = {
                    velocity: -0.1 - Math.random() * 0.1,
                    resetY: Math.random() * 50 + 20,
                    drift: (Math.random() - 0.5) * 0.02
                };

                scene.add(snowFlake);
                scene.userData.particles.push(snowFlake);
            }
        }

        function createAdvancedCar() {
            // Enhanced car with more details
            const carGeometry = new THREE.BoxGeometry(2.2, 1.2, 4.5);
            const carMaterial = new THREE.MeshLambertMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.castShadow = true;
            scene.add(car);

            // Add detailed car components
            addCarDetails();
            addCarLights();

            console.log('🏎️ Advanced car created');
        }

        function addCarDetails() {
            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(2, 1, 0.1);
            const windshieldMaterial = new THREE.MeshLambertMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.3, 1.8);
            car.add(windshield);

            // Rear windshield
            const rearWindshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            rearWindshield.position.set(0, 0.3, -1.8);
            car.add(rearWindshield);

            // Side windows
            const sideWindowGeometry = new THREE.BoxGeometry(0.1, 0.8, 2);
            const leftWindow = new THREE.Mesh(sideWindowGeometry, windshieldMaterial);
            leftWindow.position.set(-1.1, 0.3, 0);
            car.add(leftWindow);

            const rightWindow = new THREE.Mesh(sideWindowGeometry, windshieldMaterial);
            rightWindow.position.set(1.1, 0.3, 0);
            car.add(rightWindow);

            // Roof
            const roofGeometry = new THREE.BoxGeometry(2, 0.1, 3);
            const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const roof = new THREE.Mesh(roofGeometry, roofMaterial);
            roof.position.set(0, 0.65, 0);
            car.add(roof);

            // Wheels with better detail
            createAdvancedWheels();
        }

        function addCarLights() {
            // Headlights
            const headlightGeometry = new THREE.SphereGeometry(0.25, 8, 8);
            const headlightMaterial = new THREE.MeshBasicMaterial({
                color: lightsOn ? 0xffffaa : 0xcccccc,
                emissive: lightsOn ? 0x222200 : 0x000000
            });

            const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
            leftHeadlight.position.set(-0.8, 0.2, 2.2);
            car.add(leftHeadlight);

            const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
            rightHeadlight.position.set(0.8, 0.2, 2.2);
            car.add(rightHeadlight);

            // Taillights
            const taillightMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                emissive: 0x220000
            });

            const leftTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
            leftTaillight.position.set(-0.8, 0.2, -2.2);
            car.add(leftTaillight);

            const rightTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
            rightTaillight.position.set(0.8, 0.2, -2.2);
            car.add(rightTaillight);

            // Store references for light control
            car.userData.headlights = [leftHeadlight, rightHeadlight];
            car.userData.taillights = [leftTaillight, rightTaillight];

            // Headlight beams
            if (lightsOn) {
                addHeadlightBeams();
            }
        }

        function addHeadlightBeams() {
            const beamGeometry = new THREE.ConeGeometry(3, 15, 8);
            const beamMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffaa,
                transparent: true,
                opacity: 0.2
            });

            const leftBeam = new THREE.Mesh(beamGeometry, beamMaterial);
            leftBeam.position.set(-0.8, 0.2, 10);
            leftBeam.rotation.x = Math.PI / 2;
            car.add(leftBeam);

            const rightBeam = new THREE.Mesh(beamGeometry, beamMaterial);
            rightBeam.position.set(0.8, 0.2, 10);
            rightBeam.rotation.x = Math.PI / 2;
            car.add(rightBeam);

            car.userData.beams = [leftBeam, rightBeam];
        }

        function createAdvancedWheels() {
            const wheelGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.4, 16);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            // Rim geometry
            const rimGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.45, 16);
            const rimMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });

            const wheelPositions = [
                { x: -1.2, y: -0.4, z: 1.5 },   // Front left
                { x: 1.2, y: -0.4, z: 1.5 },    // Front right
                { x: -1.2, y: -0.4, z: -1.5 },  // Rear left
                { x: 1.2, y: -0.4, z: -1.5 }    // Rear right
            ];

            car.wheels = [];
            wheelPositions.forEach((pos, index) => {
                // Wheel
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                wheel.castShadow = true;
                car.add(wheel);

                // Rim
                const rim = new THREE.Mesh(rimGeometry, rimMaterial);
                rim.rotation.z = Math.PI / 2;
                rim.position.set(pos.x, pos.y, pos.z);
                car.add(rim);

                car.wheels.push({ wheel, rim });
            });
        }

        function setupAdvancedLighting() {
            // Ambient light
            const ambientLight = new THREE.AmbientLight(0x404040, isNightMode ? 0.2 : 0.6);
            scene.add(ambientLight);

            // Directional light (sun/moon)
            const directionalLight = new THREE.DirectionalLight(
                isNightMode ? 0x4444aa : 0xffffff,
                isNightMode ? 0.3 : 0.8
            );
            directionalLight.position.set(50, 50, 25);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -100;
            directionalLight.shadow.camera.right = 100;
            directionalLight.shadow.camera.top = 100;
            directionalLight.shadow.camera.bottom = -100;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 200;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Hemisphere light for natural lighting
            const hemisphereLight = new THREE.HemisphereLight(
                isNightMode ? 0x222244 : 0x87CEEB,
                isNightMode ? 0x111122 : 0x362d1d,
                isNightMode ? 0.2 : 0.4
            );
            scene.add(hemisphereLight);
        }

        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code === 'KeyT' ? 't' :
                           event.code === 'KeyB' ? 'b' :
                           event.code === 'KeyL' ? 'l' :
                           event.code === 'KeyN' ? 'n' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }

                // Special keys
                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }
                if (event.code === 'KeyL') {
                    toggleLights();
                }
                if (event.code === 'KeyN') {
                    toggleNightMode();
                }
                if (event.code === 'KeyM') {
                    changeWeather();
                }

                event.preventDefault();
            });

            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code === 'KeyT' ? 't' :
                           event.code === 'KeyB' ? 'b' :
                           event.code === 'KeyL' ? 'l' :
                           event.code === 'KeyN' ? 'n' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function updateAdvancedCar() {
            const baseAcceleration = 0.04;
            const turboMultiplier = turboActive ? 2.0 : 1.0;
            const acceleration = baseAcceleration * turboMultiplier;
            const maxSpeed = turboActive ? 1.2 : 0.8;
            const baseFriction = 0.96;
            const brakeFriction = keys.b ? 0.85 : baseFriction;
            const turnSpeed = 0.04;

            let engineForce = 0;
            let steerAngle = 0;

            // Handle input
            if (keys.w || keys.ArrowUp) {
                engineForce = acceleration;
                enginePower = Math.min(enginePower + 2, 100);
            } else {
                enginePower = Math.max(enginePower - 1, 0);
            }

            if (keys.s || keys.ArrowDown) {
                engineForce = -acceleration * 0.6;
                enginePower = Math.min(enginePower + 1, 100);
            }

            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;

            // Turbo system
            if (keys.t && turboCharge > 0) {
                turboActive = true;
                turboCharge = Math.max(turboCharge - 0.5, 0);
            } else {
                turboActive = false;
                turboCharge = Math.min(turboCharge + 0.1, 100);
            }

            // Brake force
            if (keys.b) {
                brakeForce = Math.min(brakeForce + 5, 100);
            } else {
                brakeForce = Math.max(brakeForce - 2, 0);
            }

            // Update velocity
            if (Math.abs(engineForce) > 0.001) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;

                // Consume fuel
                fuel = Math.max(fuel - 0.01, 0);

                // Create exhaust particles
                if (Math.random() < 0.2) {
                    createExhaustParticle();
                }
            }

            // Apply friction
            carVelocity.x *= brakeFriction;
            carVelocity.z *= brakeFriction;

            // Limit speed
            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }

            // Update rotation
            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.02) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }

            // Update position
            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;

            // Update distance
            distance += currentSpeed;

            // Update car mesh
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;

            // Animate wheels
            if (car.wheels) {
                const wheelRotation = currentSpeed * 5;
                car.wheels.forEach((wheelSet, index) => {
                    wheelSet.wheel.rotation.x += wheelRotation;
                    wheelSet.rim.rotation.x += wheelRotation;

                    // Front wheels steering
                    if (index < 2) {
                        wheelSet.wheel.rotation.y = steerAngle * 3;
                        wheelSet.rim.rotation.y = steerAngle * 3;
                    }
                });
            }

            // Calculate speed and RPM
            speed = currentSpeed * 120;
            rpm = Math.min(speed * 50 + enginePower * 10, 8000);

            // Check achievements
            checkAchievements();
        }

        function updateAdvancedCamera() {
            const smoothness = 0.08;
            let targetX, targetY, targetZ, lookAtX, lookAtY, lookAtZ;

            switch(cameraMode) {
                case 0: // Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 15;
                    targetY = carPosition.y + 8;
                    targetZ = carPosition.z - Math.cos(carRotation) * 15;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y + 2;
                    lookAtZ = carPosition.z;
                    break;

                case 1: // First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 0.5;
                    targetY = carPosition.y + 1.5;
                    targetZ = carPosition.z + Math.cos(carRotation) * 0.5;
                    lookAtX = carPosition.x + Math.sin(carRotation) * 20;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z + Math.cos(carRotation) * 20;
                    break;

                case 2: // Top Down
                    targetX = carPosition.x;
                    targetY = carPosition.y + 30;
                    targetZ = carPosition.z;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z;
                    break;

                case 3: // Cinematic
                    const time = Date.now() * 0.001;
                    const radius = 20;
                    targetX = carPosition.x + Math.cos(time * 0.3) * radius;
                    targetY = carPosition.y + 10 + Math.sin(time * 0.2) * 5;
                    targetZ = carPosition.z + Math.sin(time * 0.3) * radius;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y + 2;
                    lookAtZ = carPosition.z;
                    break;

                case 4: // Hood View
                    targetX = carPosition.x - Math.sin(carRotation) * 2;
                    targetY = carPosition.y + 2;
                    targetZ = carPosition.z - Math.cos(carRotation) * 2;
                    lookAtX = carPosition.x + Math.sin(carRotation) * 20;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z + Math.cos(carRotation) * 20;
                    break;
            }

            // Smooth camera movement
            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(lookAtX, lookAtY, lookAtZ);
        }

        function updateDynamicElements() {
            // Animate flags
            if (scene.userData.flags) {
                scene.userData.flags.forEach(flag => {
                    flag.userData.time += 0.02;
                    const wave = Math.sin(flag.userData.time) * 0.3;
                    flag.position.x = flag.userData.originalX + wave;
                    flag.rotation.z = wave * 0.1;
                });
            }

            // Update weather particles
            if (scene.userData.particles) {
                scene.userData.particles.forEach(particle => {
                    particle.position.y += particle.userData.velocity;

                    if (particle.userData.drift) {
                        particle.position.x += particle.userData.drift;
                    }

                    // Reset particle when it hits the ground
                    if (particle.position.y < 0) {
                        particle.position.y = particle.userData.resetY;
                        particle.position.x = (Math.random() - 0.5) * 200;
                        particle.position.z = (Math.random() - 0.5) * 200;
                    }
                });
            }
        }

        function updateGameTime() {
            gameTime += 0.01; // Advance time
            if (gameTime >= 24) gameTime = 0; // Reset at midnight

            // Update day/night cycle
            const hour = Math.floor(gameTime);
            const minute = Math.floor((gameTime - hour) * 60);

            document.getElementById('gameTime').textContent =
                `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

            // Determine day phase
            let phase = 'Day';
            if (hour >= 6 && hour < 12) phase = 'Morning';
            else if (hour >= 12 && hour < 18) phase = 'Afternoon';
            else if (hour >= 18 && hour < 22) phase = 'Evening';
            else phase = 'Night';

            document.getElementById('dayPhase').textContent = phase;

            // Auto night mode
            if ((hour >= 20 || hour < 6) && !isNightMode) {
                toggleNightMode();
            } else if (hour >= 6 && hour < 20 && isNightMode) {
                toggleNightMode();
            }
        }

        function updateSceneBackground() {
            if (isNightMode) {
                scene.background = new THREE.Color(0x0a0a1a);
                scene.fog = new THREE.Fog(0x0a0a1a, 30, 150);
            } else {
                scene.background = new THREE.Color(0x87CEEB);
                scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
            }
        }

        function createExhaustParticle() {
            const exhaustGeometry = new THREE.SphereGeometry(0.1, 6, 6);
            const exhaustMaterial = new THREE.MeshBasicMaterial({
                color: 0x666666,
                transparent: true,
                opacity: 0.5
            });
            const exhaust = new THREE.Mesh(exhaustGeometry, exhaustMaterial);

            // Position behind car
            const exhaustX = carPosition.x - Math.sin(carRotation) * 2.5;
            const exhaustZ = carPosition.z - Math.cos(carRotation) * 2.5;
            exhaust.position.set(exhaustX, carPosition.y + 0.2, exhaustZ);

            scene.add(exhaust);

            // Animate and remove
            let opacity = 0.5;
            const animate = () => {
                opacity -= 0.02;
                exhaust.material.opacity = opacity;
                exhaust.position.y += 0.05;
                exhaust.scale.multiplyScalar(1.02);

                if (opacity <= 0) {
                    scene.remove(exhaust);
                } else {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function checkAchievements() {
            // Speed Demon
            if (speed >= 100 && !achievements[1].unlocked) {
                unlockAchievement(1);
            }

            // Long Distance
            if (distance >= 1000 && !achievements[2].unlocked) {
                unlockAchievement(2);
            }

            // Night Rider
            if (isNightMode && speed > 20 && !achievements[3].unlocked) {
                unlockAchievement(3);
            }
        }

        function unlockAchievement(index) {
            achievements[index].unlocked = true;
            showNotification(`🏆 Achievement Unlocked: ${achievements[index].name}!`);
            updateAchievementsDisplay();
        }

        function updateAchievementsDisplay() {
            const achievementsList = document.getElementById('achievementsList');
            achievementsList.innerHTML = '';

            achievements.filter(a => a.unlocked).forEach(achievement => {
                const item = document.createElement('div');
                item.className = 'achievement-item';
                item.innerHTML = `
                    <span class="achievement-icon">${achievement.icon}</span>
                    <span>${achievement.name}</span>
                `;
                achievementsList.appendChild(item);
            });
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            document.getElementById('dashCamera').textContent = cameraModes[cameraMode];
            showNotification(`📷 ${cameraModes[cameraMode]} Camera`);
        }

        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            rpm = 0;
            fuel = 100;
            distance = 0;
            showNotification('🔄 Vehicle Reset');
        }

        function toggleLights() {
            lightsOn = !lightsOn;

            // Update headlight materials
            if (car.userData.headlights) {
                car.userData.headlights.forEach(light => {
                    light.material.color.setHex(lightsOn ? 0xffffaa : 0xcccccc);
                    light.material.emissive.setHex(lightsOn ? 0x222200 : 0x000000);
                });
            }

            // Add/remove headlight beams
            if (lightsOn && !car.userData.beams) {
                addHeadlightBeams();
            } else if (!lightsOn && car.userData.beams) {
                car.userData.beams.forEach(beam => car.remove(beam));
                car.userData.beams = null;
            }

            showNotification(lightsOn ? '💡 Lights On' : '🔦 Lights Off');
        }

        function toggleNightMode() {
            isNightMode = !isNightMode;
            updateSceneBackground();
            showNotification(isNightMode ? '🌙 Night Mode' : '☀️ Day Mode');
        }

        function changeWeather() {
            currentWeather = (currentWeather + 1) % weatherStates.length;
            const weather = weatherStates[currentWeather];

            document.getElementById('weatherIcon').textContent = weather.icon;
            document.getElementById('weatherText').textContent = weather.text;
            document.getElementById('temperature').textContent = `${weather.temp}°C`;

            // Clear existing particles
            if (scene.userData.particles) {
                scene.userData.particles.forEach(particle => scene.remove(particle));
                scene.userData.particles = [];
            }

            // Create new weather particles
            createParticleSystem();

            showNotification(`🌦️ Weather: ${weather.text}`);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification-advanced';
            notification.textContent = message;
            document.getElementById('gameContainer').appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        function updateAdvancedUI() {
            // Update dashboard
            document.getElementById('dashSpeed').textContent = `${Math.round(speed)} km/h`;
            document.getElementById('dashRPM').textContent = Math.round(rpm);
            document.getElementById('dashGear').textContent = speed > 5 ? 'D' : 'P';
            document.getElementById('dashFuel').textContent = `${Math.round(fuel)}%`;
            document.getElementById('dashDistance').textContent = `${Math.round(distance)} m`;

            // Update speedometer
            document.getElementById('speedValue').textContent = Math.round(speed);
            const speedNeedle = document.getElementById('speedNeedle');
            const angle = (speed / 200) * 180 - 90; // 0-200 km/h range
            speedNeedle.style.transform = `rotate(${angle}deg)`;

            // Update performance bars
            document.getElementById('engineBar').style.width = `${enginePower}%`;
            document.getElementById('brakeBar').style.width = `${brakeForce}%`;
            document.getElementById('turboBar').style.width = `${turboCharge}%`;

            // Update weather
            const weather = weatherStates[currentWeather];
            document.getElementById('weatherIcon').textContent = weather.icon;
            document.getElementById('weatherText').textContent = weather.text;
            document.getElementById('temperature').textContent = `${weather.temp}°C`;
        }

        function animate() {
            requestAnimationFrame(animate);

            updateAdvancedCar();
            updateAdvancedCamera();
            updateDynamicElements();
            updateGameTime();
            updateAdvancedUI();

            renderer.render(scene, camera);
        }

        window.addEventListener('load', () => {
            console.log('🏎️ Starting Advanced Car Simulator...');
            init();
        });
    </script>
</body>
</html>
