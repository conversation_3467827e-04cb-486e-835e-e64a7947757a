<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗺️ Google Maps Car Driving</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            color: white;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        canvas {
            display: block;
        }

        .maps-hud {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .location-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #4285f4;
            min-width: 300px;
            pointer-events: auto;
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4285f4;
            text-align: center;
            margin-bottom: 1rem;
        }

        .location-search {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #4285f4;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .location-search::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .quick-locations {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .location-btn {
            padding: 0.8rem;
            background: rgba(66, 133, 244, 0.2);
            border: 1px solid rgba(66, 133, 244, 0.5);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            text-align: center;
        }

        .location-btn:hover {
            background: rgba(66, 133, 244, 0.4);
            transform: translateY(-2px);
        }

        .current-location {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(66, 133, 244, 0.3);
            margin-top: 1rem;
        }

        .location-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .maps-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #4285f4;
            pointer-events: auto;
        }

        .control-btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: rgba(66, 133, 244, 0.2);
            border: 1px solid rgba(66, 133, 244, 0.5);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .control-btn:hover {
            background: rgba(66, 133, 244, 0.4);
        }

        .control-btn:last-child {
            margin-bottom: 0;
        }

        .minimap-container {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 250px;
            height: 200px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #4285f4;
            border-radius: 10px;
            overflow: hidden;
            pointer-events: auto;
        }

        #miniMap {
            width: 100%;
            height: 100%;
        }

        .speed-hud {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #4285f4;
            text-align: center;
        }

        .speed-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4285f4;
            margin-bottom: 0.5rem;
        }

        .speed-unit {
            font-size: 1rem;
            opacity: 0.8;
        }

        .coordinates {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            opacity: 0.7;
        }

        .loading-maps {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #ea4335 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
        }

        .loading-title {
            font-size: 3rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .loading-progress {
            width: 400px;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 2rem 0;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f0f0f0);
            border-radius: 4px;
            animation: loading 3s ease-in-out;
        }

        @keyframes loading {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .api-notice {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
            text-align: center;
            max-width: 500px;
        }

        .api-key-input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #4285f4;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
            margin: 1rem 0;
        }

        .start-btn {
            padding: 1rem 2rem;
            background: #4285f4;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .start-btn:hover {
            background: #3367d6;
            transform: scale(1.05);
        }

        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #4285f4;
            text-align: center;
            z-index: 200;
            animation: fadeInOut 4s ease-in-out;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .error-message {
            background: rgba(234, 67, 53, 0.2);
            border: 2px solid #ea4335;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading" class="loading-maps">
            <div class="loading-title">🗺️ GOOGLE MAPS DRIVING</div>
            <p style="font-size: 1.2rem; margin-bottom: 1rem;">Initializing Real-World Driving Experience...</p>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>

            <div class="api-notice">
                <h3>🔑 Google Maps API Required</h3>
                <p>To use real Google Maps data, you need a Google Maps JavaScript API key.</p>
                <p><strong>Get your free API key:</strong></p>
                <ol style="text-align: left; margin: 1rem 0;">
                    <li>Go to <a href="https://console.cloud.google.com/" target="_blank" style="color: #4285f4;">Google Cloud Console</a></li>
                    <li>Create a new project or select existing</li>
                    <li>Enable "Maps JavaScript API"</li>
                    <li>Create credentials (API Key)</li>
                    <li>Copy and paste the key below</li>
                </ol>

                <input type="text" id="apiKeyInput" class="api-key-input" placeholder="Paste your Google Maps API key here...">
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <button class="start-btn" onclick="initializeWithAPI()">🚀 Start Driving</button>

                <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                    <strong>Demo Mode:</strong> Click below to try with simulated data
                </p>
                <button class="start-btn" onclick="initializeDemoMode()" style="background: #34a853;">🎮 Demo Mode</button>
            </div>
        </div>

        <div class="maps-hud" id="hud" style="display: none;">
            <!-- Location Panel -->
            <div class="location-panel">
                <div class="panel-title">🗺️ Location Control</div>

                <input type="text" class="location-search" id="locationSearch" placeholder="Search for a location...">

                <div class="quick-locations">
                    <button class="location-btn" onclick="goToLocation('New York, NY')">🏙️ New York</button>
                    <button class="location-btn" onclick="goToLocation('Los Angeles, CA')">🌴 Los Angeles</button>
                    <button class="location-btn" onclick="goToLocation('London, UK')">🇬🇧 London</button>
                    <button class="location-btn" onclick="goToLocation('Tokyo, Japan')">🇯🇵 Tokyo</button>
                    <button class="location-btn" onclick="goToLocation('Paris, France')">🇫🇷 Paris</button>
                    <button class="location-btn" onclick="getCurrentLocation()">📍 My Location</button>
                </div>

                <div class="current-location">
                    <div class="location-info">
                        <span>Current:</span>
                        <span id="currentLocationName">Loading...</span>
                    </div>
                    <div class="location-info">
                        <span>Lat:</span>
                        <span id="currentLat">0.000</span>
                    </div>
                    <div class="location-info">
                        <span>Lng:</span>
                        <span id="currentLng">0.000</span>
                    </div>
                </div>
            </div>

            <!-- Maps Controls -->
            <div class="maps-controls">
                <button class="control-btn" onclick="switchMapType()">🗺️ Map Type</button>
                <button class="control-btn" onclick="toggleTraffic()">🚦 Traffic</button>
                <button class="control-btn" onclick="switchCamera()">📷 Camera</button>
                <button class="control-btn" onclick="resetCar()">🔄 Reset</button>
            </div>

            <!-- Speed HUD -->
            <div class="speed-hud">
                <div class="speed-value" id="speedValue">0</div>
                <div class="speed-unit">KM/H</div>
                <div class="coordinates">
                    <div>Lat: <span id="carLat">0.000</span></div>
                    <div>Lng: <span id="carLng">0.000</span></div>
                </div>
            </div>

            <!-- Minimap -->
            <div class="minimap-container">
                <div id="miniMap"></div>
            </div>
        </div>

        <canvas id="canvas"></canvas>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Google Maps Car Driving Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let cameraMode = 0;

        // Google Maps Integration
        let map, miniMap, geocoder, directionsService, directionsRenderer;
        let currentLocation = { lat: 40.7128, lng: -74.0060 }; // Default: New York
        let mapType = 'roadmap';
        let trafficEnabled = false;
        let isAPIMode = false;
        let apiKey = '';

        // Real-world coordinates
        let realWorldLat = currentLocation.lat;
        let realWorldLng = currentLocation.lng;

        const cameraModes = ['Third Person', 'First Person', 'Satellite View', 'Street View'];

        // Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false
        };

        function initializeWithAPI() {
            apiKey = document.getElementById('apiKeyInput').value.trim();

            if (!apiKey) {
                showError('Please enter your Google Maps API key');
                return;
            }

            // Validate API key format (basic check)
            if (apiKey.length < 20) {
                showError('Invalid API key format. Please check your key.');
                return;
            }

            isAPIMode = true;
            loadGoogleMapsAPI();
        }

        function initializeDemoMode() {
            isAPIMode = false;
            startGame();
        }

        function loadGoogleMapsAPI() {
            // Create script tag to load Google Maps API
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry,places&callback=initGoogleMaps`;
            script.onerror = () => {
                showError('Failed to load Google Maps API. Please check your API key and internet connection.');
            };
            document.head.appendChild(script);
        }

        function initGoogleMaps() {
            console.log('🗺️ Google Maps API loaded successfully');

            // Initialize Google Maps services
            geocoder = new google.maps.Geocoder();
            directionsService = new google.maps.DirectionsService();
            directionsRenderer = new google.maps.DirectionsRenderer();

            // Initialize minimap
            initMiniMap();

            // Start the game
            startGame();
        }

        function startGame() {
            console.log('🚗 Starting Google Maps Car Driving...');

            // Hide loading screen
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('hud').style.display = 'block';
                showNotification(isAPIMode ? '🗺️ Real Google Maps Loaded!' : '🎮 Demo Mode Active');
            }, 3000);

            // Initialize 3D scene
            init3DScene();

            // Setup location search
            setupLocationSearch();

            // Start game loop
            animate();
        }

        function init3DScene() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 8, -15);

            // Create renderer
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('canvas'),
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Create environment based on mode
            if (isAPIMode) {
                createRealWorldEnvironment();
            } else {
                createDemoEnvironment();
            }

            createCar();
            setupLighting();
            setupEventListeners();

            console.log('✅ 3D scene initialized');
        }

        function createRealWorldEnvironment() {
            // Create terrain based on real elevation data
            createRealisticTerrain();

            // Create roads from Google Maps data
            createRealRoads();

            // Create buildings from Google Maps data
            createRealBuildings();

            // Add real-world landmarks
            addLandmarks();
        }

        function createRealisticTerrain() {
            // Ground with realistic texture
            const groundGeometry = new THREE.PlaneGeometry(1000, 1000, 100, 100);

            // Simulate elevation data (in real implementation, use Google Elevation API)
            const vertices = groundGeometry.attributes.position.array;
            for (let i = 0; i < vertices.length; i += 3) {
                // Add some realistic elevation variation
                vertices[i + 1] = Math.sin(vertices[i] * 0.01) * Math.cos(vertices[i + 2] * 0.01) * 2;
            }
            groundGeometry.attributes.position.needsUpdate = true;
            groundGeometry.computeVertexNormals();

            const groundMaterial = new THREE.MeshLambertMaterial({
                color: 0x4a5d23,
                wireframe: false
            });

            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
        }

        function createRealRoads() {
            // In real implementation, use Google Roads API to get road data
            // For now, create realistic road network
            const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            // Main highways (simulating real road data)
            const highways = [
                { start: [-200, 0], end: [200, 0], width: 20 },
                { start: [0, -200], end: [0, 200], width: 15 },
                { start: [-100, -100], end: [100, 100], width: 12 },
                { start: [-100, 100], end: [100, -100], width: 12 }
            ];

            highways.forEach(highway => {
                const length = Math.sqrt(
                    Math.pow(highway.end[0] - highway.start[0], 2) +
                    Math.pow(highway.end[1] - highway.start[1], 2)
                );

                const roadGeometry = new THREE.PlaneGeometry(length, highway.width);
                const road = new THREE.Mesh(roadGeometry, roadMaterial);

                // Position and rotate road
                road.position.set(
                    (highway.start[0] + highway.end[0]) / 2,
                    0.01,
                    (highway.start[1] + highway.end[1]) / 2
                );

                const angle = Math.atan2(
                    highway.end[1] - highway.start[1],
                    highway.end[0] - highway.start[0]
                );
                road.rotation.x = -Math.PI / 2;
                road.rotation.z = angle;
                road.receiveShadow = true;
                scene.add(road);

                // Add road markings
                addRoadMarkings(road, length, highway.width);
            });
        }

        function addRoadMarkings(road, length, width) {
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            // Center line
            const centerLineGeometry = new THREE.PlaneGeometry(length * 0.8, 0.3);
            const centerLine = new THREE.Mesh(centerLineGeometry, markingMaterial);
            centerLine.position.copy(road.position);
            centerLine.position.y += 0.001;
            centerLine.rotation.copy(road.rotation);
            scene.add(centerLine);

            // Lane markings
            for (let i = -width/4; i <= width/4; i += width/8) {
                if (i === 0) continue; // Skip center

                const laneGeometry = new THREE.PlaneGeometry(length * 0.9, 0.2);
                const laneLine = new THREE.Mesh(laneGeometry, markingMaterial);
                laneLine.position.copy(road.position);
                laneLine.position.y += 0.001;

                // Offset for lane position
                const offsetX = Math.cos(road.rotation.z + Math.PI/2) * i;
                const offsetZ = Math.sin(road.rotation.z + Math.PI/2) * i;
                laneLine.position.x += offsetX;
                laneLine.position.z += offsetZ;

                laneLine.rotation.copy(road.rotation);
                scene.add(laneLine);
            }
        }

        function createRealBuildings() {
            // In real implementation, use Google Places API to get building data
            // For now, create realistic building distribution
            const buildingConfigs = [
                // Downtown area (high-rise buildings)
                ...generateBuildingCluster(0, 0, 50, 20, 15, 40),
                // Residential areas (smaller buildings)
                ...generateBuildingCluster(100, 100, 30, 8, 5, 15),
                ...generateBuildingCluster(-100, -100, 30, 8, 5, 15),
                // Commercial areas (medium buildings)
                ...generateBuildingCluster(150, 0, 25, 12, 8, 25),
                ...generateBuildingCluster(-150, 0, 25, 12, 8, 25)
            ];

            buildingConfigs.forEach(config => {
                createRealisticBuilding(config);
            });
        }

        function generateBuildingCluster(centerX, centerZ, count, minSize, minHeight, maxHeight) {
            const buildings = [];

            for (let i = 0; i < count; i++) {
                const angle = (i / count) * Math.PI * 2;
                const radius = 20 + Math.random() * 80;
                const x = centerX + Math.cos(angle) * radius + (Math.random() - 0.5) * 40;
                const z = centerZ + Math.sin(angle) * radius + (Math.random() - 0.5) * 40;

                buildings.push({
                    x: x,
                    z: z,
                    width: minSize + Math.random() * 10,
                    depth: minSize + Math.random() * 10,
                    height: minHeight + Math.random() * (maxHeight - minHeight),
                    color: getRealisticBuildingColor()
                });
            }

            return buildings;
        }

        function getRealisticBuildingColor() {
            const colors = [
                0x8B4513, // Brown
                0x696969, // Gray
                0x4682B4, // Steel Blue
                0x2F4F4F, // Dark Slate Gray
                0x800000, // Maroon
                0x708090, // Slate Gray
                0x556B2F, // Dark Olive Green
                0x483D8B  // Dark Slate Blue
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        function createRealisticBuilding(config) {
            const buildingGeometry = new THREE.BoxGeometry(config.width, config.height, config.depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({ color: config.color });
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);

            building.position.set(config.x, config.height / 2, config.z);
            building.castShadow = true;
            building.receiveShadow = true;
            scene.add(building);

            // Add windows for realism
            addBuildingWindows(building, config);

            // Add rooftop details
            addRooftopDetails(building, config);
        }

        function addBuildingWindows(building, config) {
            const windowMaterial = new THREE.MeshBasicMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });

            // Calculate window grid
            const windowsPerFloor = Math.floor(config.width / 3);
            const floors = Math.floor(config.height / 4);

            for (let floor = 1; floor < floors; floor++) {
                for (let window = 0; window < windowsPerFloor; window++) {
                    // Front face windows
                    const windowGeometry = new THREE.PlaneGeometry(1.5, 2);
                    const windowMesh = new THREE.Mesh(windowGeometry, windowMaterial);

                    windowMesh.position.set(
                        -config.width/2 + (window + 0.5) * (config.width / windowsPerFloor),
                        -config.height/2 + floor * 4,
                        config.depth/2 + 0.01
                    );

                    building.add(windowMesh);

                    // Back face windows
                    const backWindow = windowMesh.clone();
                    backWindow.position.z = -config.depth/2 - 0.01;
                    backWindow.rotation.y = Math.PI;
                    building.add(backWindow);
                }
            }
        }

        function addRooftopDetails(building, config) {
            // Antenna
            if (config.height > 20) {
                const antennaGeometry = new THREE.CylinderGeometry(0.1, 0.1, 5);
                const antennaMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
                const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
                antenna.position.set(0, config.height/2 + 2.5, 0);
                building.add(antenna);
            }

            // Rooftop equipment
            const equipmentCount = Math.floor(Math.random() * 3) + 1;
            for (let i = 0; i < equipmentCount; i++) {
                const equipGeometry = new THREE.BoxGeometry(2, 1, 2);
                const equipMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
                const equipment = new THREE.Mesh(equipGeometry, equipMaterial);

                equipment.position.set(
                    (Math.random() - 0.5) * config.width * 0.6,
                    config.height/2 + 0.5,
                    (Math.random() - 0.5) * config.depth * 0.6
                );

                building.add(equipment);
            }
        }

        function addLandmarks() {
            // Add recognizable landmarks based on location
            // This would use Google Places API in real implementation

            // Example: Central Park-style area
            createParkArea(50, 50, 40, 30);

            // Example: Stadium
            createStadium(-80, 80);

            // Example: Airport
            createAirport(200, -200);
        }

        function createParkArea(x, z, width, depth) {
            // Park ground
            const parkGeometry = new THREE.PlaneGeometry(width, depth);
            const parkMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const park = new THREE.Mesh(parkGeometry, parkMaterial);
            park.rotation.x = -Math.PI / 2;
            park.position.set(x, 0.02, z);
            scene.add(park);

            // Trees
            for (let i = 0; i < 20; i++) {
                const treeX = x + (Math.random() - 0.5) * width * 0.8;
                const treeZ = z + (Math.random() - 0.5) * depth * 0.8;
                createTree(treeX, treeZ);
            }

            // Paths
            const pathMaterial = new THREE.MeshLambertMaterial({ color: 0x8B7355 });
            const pathGeometry = new THREE.PlaneGeometry(width * 0.9, 2);
            const path = new THREE.Mesh(pathGeometry, pathMaterial);
            path.rotation.x = -Math.PI / 2;
            path.position.set(x, 0.03, z);
            scene.add(path);
        }

        function createTree(x, z) {
            // Trunk
            const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 6);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(x, 3, z);
            trunk.castShadow = true;
            scene.add(trunk);

            // Leaves
            const leavesGeometry = new THREE.SphereGeometry(3, 12, 12);
            const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
            leaves.position.set(x, 8, z);
            leaves.castShadow = true;
            scene.add(leaves);
        }

        function createStadium(x, z) {
            // Stadium structure
            const stadiumGeometry = new THREE.CylinderGeometry(40, 45, 15, 32);
            const stadiumMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
            const stadium = new THREE.Mesh(stadiumGeometry, stadiumMaterial);
            stadium.position.set(x, 7.5, z);
            stadium.castShadow = true;
            scene.add(stadium);

            // Field
            const fieldGeometry = new THREE.CylinderGeometry(35, 35, 0.2, 32);
            const fieldMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const field = new THREE.Mesh(fieldGeometry, fieldMaterial);
            field.position.set(x, 0.1, z);
            scene.add(field);
        }

        function createAirport(x, z) {
            // Terminal building
            const terminalGeometry = new THREE.BoxGeometry(60, 8, 20);
            const terminalMaterial = new THREE.MeshLambertMaterial({ color: 0xD3D3D3 });
            const terminal = new THREE.Mesh(terminalGeometry, terminalMaterial);
            terminal.position.set(x, 4, z);
            terminal.castShadow = true;
            scene.add(terminal);

            // Runway
            const runwayGeometry = new THREE.PlaneGeometry(200, 15);
            const runwayMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
            const runway = new THREE.Mesh(runwayGeometry, runwayMaterial);
            runway.rotation.x = -Math.PI / 2;
            runway.position.set(x, 0.01, z - 40);
            scene.add(runway);

            // Runway markings
            const markingGeometry = new THREE.PlaneGeometry(180, 2);
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFFFF });
            const marking = new THREE.Mesh(markingGeometry, markingMaterial);
            marking.rotation.x = -Math.PI / 2;
            marking.position.set(x, 0.02, z - 40);
            scene.add(marking);
        }

        function createDemoEnvironment() {
            // Simple demo environment when API is not available
            const groundGeometry = new THREE.PlaneGeometry(500, 500);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Demo roads
            const roadGeometry = new THREE.PlaneGeometry(500, 20);
            const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const road = new THREE.Mesh(roadGeometry, roadMaterial);
            road.rotation.x = -Math.PI / 2;
            road.position.y = 0.01;
            scene.add(road);

            // Demo buildings
            for (let i = 0; i < 10; i++) {
                const x = (Math.random() - 0.5) * 400;
                const z = (Math.random() - 0.5) * 400;
                if (Math.abs(z) > 30) { // Don't place on road
                    createRealisticBuilding({
                        x: x, z: z,
                        width: 10 + Math.random() * 20,
                        depth: 10 + Math.random() * 20,
                        height: 10 + Math.random() * 30,
                        color: getRealisticBuildingColor()
                    });
                }
            }
        }

        function createCar() {
            // Car body
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshLambertMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.castShadow = true;
            scene.add(car);

            // Add car details
            addCarDetails();

            console.log('🚗 Car created for Google Maps driving');
        }

        function addCarDetails() {
            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(1.8, 0.8, 0.1);
            const windshieldMaterial = new THREE.MeshLambertMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.2, 1.5);
            car.add(windshield);

            // Wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 8);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            const wheelPositions = [
                { x: -1, y: -0.3, z: 1.2 },
                { x: 1, y: -0.3, z: 1.2 },
                { x: -1, y: -0.3, z: -1.2 },
                { x: 1, y: -0.3, z: -1.2 }
            ];

            car.wheels = [];
            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                car.add(wheel);
                car.wheels.push(wheel);
            });
        }

        function setupLighting() {
            // Ambient light
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // Directional light (sun)
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 25);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -200;
            directionalLight.shadow.camera.right = 200;
            directionalLight.shadow.camera.top = 200;
            directionalLight.shadow.camera.bottom = -200;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 300;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
        }

        function setupEventListeners() {
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }

                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }
            });

            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function setupLocationSearch() {
            const searchInput = document.getElementById('locationSearch');

            if (isAPIMode && geocoder) {
                // Setup autocomplete for real API
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        const location = e.target.value;
                        goToLocation(location);
                    }
                });
            } else {
                // Demo mode - simulate search
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        showNotification('🎮 Demo Mode: Search simulation');
                    }
                });
            }
        }

        function initMiniMap() {
            if (!isAPIMode) {
                // Demo minimap
                const minimapDiv = document.getElementById('miniMap');
                minimapDiv.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #4285f4;">🗺️ Demo Mode<br>Minimap</div>';
                return;
            }

            // Real Google Maps minimap
            miniMap = new google.maps.Map(document.getElementById('miniMap'), {
                center: currentLocation,
                zoom: 15,
                mapTypeId: mapType,
                disableDefaultUI: true,
                styles: [
                    {
                        featureType: 'all',
                        elementType: 'labels',
                        stylers: [{ visibility: 'simplified' }]
                    }
                ]
            });

            // Add car marker to minimap
            const carMarker = new google.maps.Marker({
                position: currentLocation,
                map: miniMap,
                icon: {
                    path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                    scale: 6,
                    fillColor: '#ff4444',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2
                }
            });

            // Store marker reference
            miniMap.carMarker = carMarker;
        }

        function goToLocation(locationName) {
            if (!isAPIMode) {
                showNotification('🎮 Demo Mode: Location search not available');
                return;
            }

            geocoder.geocode({ address: locationName }, (results, status) => {
                if (status === 'OK') {
                    const location = results[0].geometry.location;
                    currentLocation = {
                        lat: location.lat(),
                        lng: location.lng()
                    };

                    // Update real-world coordinates
                    realWorldLat = currentLocation.lat;
                    realWorldLng = currentLocation.lng;

                    // Update UI
                    updateLocationDisplay(locationName);

                    // Regenerate environment for new location
                    regenerateEnvironment();

                    // Update minimap
                    if (miniMap) {
                        miniMap.setCenter(currentLocation);
                        miniMap.carMarker.setPosition(currentLocation);
                    }

                    showNotification(`📍 Moved to ${locationName}`);
                } else {
                    showError('Location not found. Please try a different search.');
                }
            });
        }

        function getCurrentLocation() {
            if (!navigator.geolocation) {
                showError('Geolocation is not supported by this browser.');
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    realWorldLat = currentLocation.lat;
                    realWorldLng = currentLocation.lng;

                    // Get location name
                    if (isAPIMode && geocoder) {
                        geocoder.geocode({ location: currentLocation }, (results, status) => {
                            if (status === 'OK' && results[0]) {
                                updateLocationDisplay(results[0].formatted_address);
                            }
                        });
                    }

                    regenerateEnvironment();

                    if (miniMap) {
                        miniMap.setCenter(currentLocation);
                        miniMap.carMarker.setPosition(currentLocation);
                    }

                    showNotification('📍 Using your current location');
                },
                (error) => {
                    showError('Unable to get your location. Using default location.');
                }
            );
        }

        function regenerateEnvironment() {
            // Clear existing environment
            const objectsToRemove = [];
            scene.traverse((object) => {
                if (object !== car && object.type === 'Mesh') {
                    objectsToRemove.push(object);
                }
            });

            objectsToRemove.forEach(object => {
                scene.remove(object);
            });

            // Create new environment
            if (isAPIMode) {
                createRealWorldEnvironment();
            } else {
                createDemoEnvironment();
            }

            console.log('🗺️ Environment regenerated for new location');
        }

        function updateLocationDisplay(locationName) {
            document.getElementById('currentLocationName').textContent = locationName;
            document.getElementById('currentLat').textContent = realWorldLat.toFixed(6);
            document.getElementById('currentLng').textContent = realWorldLng.toFixed(6);
        }

        function switchMapType() {
            if (!isAPIMode) {
                showNotification('🎮 Demo Mode: Map type switching not available');
                return;
            }

            const mapTypes = ['roadmap', 'satellite', 'hybrid', 'terrain'];
            const currentIndex = mapTypes.indexOf(mapType);
            mapType = mapTypes[(currentIndex + 1) % mapTypes.length];

            if (miniMap) {
                miniMap.setMapTypeId(mapType);
            }

            showNotification(`🗺️ Map type: ${mapType}`);
        }

        function toggleTraffic() {
            if (!isAPIMode) {
                showNotification('🎮 Demo Mode: Traffic data not available');
                return;
            }

            trafficEnabled = !trafficEnabled;

            if (miniMap) {
                const trafficLayer = new google.maps.TrafficLayer();
                if (trafficEnabled) {
                    trafficLayer.setMap(miniMap);
                } else {
                    trafficLayer.setMap(null);
                }
            }

            showNotification(trafficEnabled ? '🚦 Traffic ON' : '🚦 Traffic OFF');
        }

        function updateCar() {
            const acceleration = 0.03;
            const maxSpeed = 0.8;
            const friction = 0.96;
            const turnSpeed = 0.04;

            let engineForce = 0;
            let steerAngle = 0;

            if (keys.w || keys.ArrowUp) engineForce = acceleration;
            if (keys.s || keys.ArrowDown) engineForce = -acceleration * 0.5;
            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;

            if (Math.abs(engineForce) > 0.001) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;
            }

            carVelocity.x *= friction;
            carVelocity.z *= friction;

            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }

            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.01) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }

            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;

            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;

            // Animate wheels
            if (car.wheels) {
                const wheelRotation = currentSpeed * 5;
                car.wheels.forEach((wheel, index) => {
                    wheel.rotation.x += wheelRotation;
                    if (index < 2) {
                        wheel.rotation.y = steerAngle * 3;
                    }
                });
            }

            speed = currentSpeed * 120;

            // Update real-world coordinates (simplified conversion)
            const metersPerUnit = 10; // 1 3D unit = 10 meters
            const deltaLat = (carVelocity.z * metersPerUnit) / 111320; // Approximate meters per degree latitude
            const deltaLng = (carVelocity.x * metersPerUnit) / (111320 * Math.cos(realWorldLat * Math.PI / 180));

            realWorldLat += deltaLat;
            realWorldLng += deltaLng;

            // Update minimap car position
            if (isAPIMode && miniMap && miniMap.carMarker) {
                const newPosition = { lat: realWorldLat, lng: realWorldLng };
                miniMap.carMarker.setPosition(newPosition);
                miniMap.setCenter(newPosition);
            }
        }

        function updateCamera() {
            const smoothness = 0.1;
            let targetX, targetY, targetZ;

            switch(cameraMode) {
                case 0: // Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 12;
                    targetY = carPosition.y + 6;
                    targetZ = carPosition.z - Math.cos(carRotation) * 12;
                    break;
                case 1: // First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 0.5;
                    targetY = carPosition.y + 1.5;
                    targetZ = carPosition.z + Math.cos(carRotation) * 0.5;
                    break;
                case 2: // Satellite View
                    targetX = carPosition.x;
                    targetY = carPosition.y + 50;
                    targetZ = carPosition.z;
                    break;
                case 3: // Street View
                    targetX = carPosition.x - Math.sin(carRotation) * 8;
                    targetY = carPosition.y + 3;
                    targetZ = carPosition.z - Math.cos(carRotation) * 8;
                    break;
            }

            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(carPosition.x, carPosition.y, carPosition.z);
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            showNotification(`📷 ${cameraModes[cameraMode]}`);
        }

        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            showNotification('🔄 Car reset');
        }

        function updateUI() {
            document.getElementById('speedValue').textContent = Math.round(speed);
            document.getElementById('carLat').textContent = realWorldLat.toFixed(6);
            document.getElementById('carLng').textContent = realWorldLng.toFixed(6);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.getElementById('gameContainer').appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function animate() {
            requestAnimationFrame(animate);

            updateCar();
            updateCamera();
            updateUI();

            renderer.render(scene, camera);
        }

        window.initGoogleMaps = initGoogleMaps; // Make function globally available
    </script>
</body>
</html>
