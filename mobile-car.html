<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Mobile Car Driving</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: Arial, sans-serif;
            color: white;
            overflow: hidden;
            touch-action: none;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        .mobile-hud {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            font-size: 0.9rem;
            z-index: 100;
        }
        
        .speed-display {
            font-size: 2rem;
            font-weight: bold;
            color: #00f5ff;
            text-align: center;
            margin-bottom: 0.5rem;
        }
        
        .mobile-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 100;
        }
        
        .control-stick {
            width: 120px;
            height: 120px;
            background: rgba(0, 0, 0, 0.6);
            border: 3px solid #00f5ff;
            border-radius: 50%;
            position: relative;
            touch-action: none;
        }
        
        .stick-knob {
            width: 40px;
            height: 40px;
            background: #00f5ff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.1s ease;
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }
        
        .control-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .control-btn {
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid #00f5ff;
            border-radius: 50%;
            color: #00f5ff;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            touch-action: none;
            cursor: pointer;
        }
        
        .control-btn:active {
            background: rgba(0, 245, 255, 0.3);
            transform: scale(0.95);
        }
        
        .camera-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            font-size: 0.8rem;
            z-index: 100;
        }
        
        .notification {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 1rem 2rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            text-align: center;
            z-index: 200;
            animation: fadeInOut 2s ease-in-out;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 1000;
        }
        
        .loading h2 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .loading-bar {
            width: 200px;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 1rem auto;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #00f5ff, #0080ff);
            border-radius: 3px;
            animation: loading 2s ease-in-out;
        }
        
        @keyframes loading {
            0% { width: 0%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="loading" class="loading">
            <h2>🚗 Mobile Car Driving</h2>
            <p>Loading 3D engine...</p>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
        
        <div class="mobile-hud" id="hud" style="display: none;">
            <div class="speed-display" id="speed">0</div>
            <div>KM/H</div>
            <div style="margin-top: 0.5rem;">
                <div>Score: <span id="score">0</span></div>
                <div>Time: <span id="time">0:00</span></div>
            </div>
        </div>
        
        <div class="camera-info" id="cameraInfo" style="display: none;">
            <span id="cameraMode">Third Person</span>
        </div>
        
        <div class="mobile-controls" id="controls" style="display: none;">
            <div class="control-stick" id="steeringStick">
                <div class="stick-knob" id="steeringKnob"></div>
            </div>
            
            <div class="control-stick" id="accelerationStick">
                <div class="stick-knob" id="accelerationKnob"></div>
            </div>
            
            <div class="control-buttons">
                <div class="control-btn" id="cameraBtn">📷</div>
                <div class="control-btn" id="resetBtn">🔄</div>
            </div>
        </div>
        
        <canvas id="canvas"></canvas>
    </div>
    
    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Mobile Game Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let cameraMode = 0;
        let gameTime = 0;
        let score = 0;
        
        const cameraModes = ['Third Person', 'First Person', 'Top Down'];
        
        // Mobile input handling
        let steeringInput = 0;
        let accelerationInput = 0;
        let isSteeringActive = false;
        let isAccelerationActive = false;
        
        // Touch handling
        let touches = {};
        
        function init() {
            console.log('📱 Initializing Mobile Car Game...');
            
            // Hide loading screen after delay
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('hud').style.display = 'block';
                document.getElementById('cameraInfo').style.display = 'block';
                document.getElementById('controls').style.display = 'flex';
            }, 2000);
            
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);
            
            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            
            // Create renderer
            renderer = new THREE.WebGLRenderer({ 
                canvas: document.getElementById('canvas'), 
                antialias: true 
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            
            // Create environment
            createEnvironment();
            createCar();
            setupLighting();
            setupMobileControls();
            
            // Start game loop
            animate();
            
            console.log('✅ Mobile game initialized!');
        }
        
        function createEnvironment() {
            // Ground
            const groundGeometry = new THREE.PlaneGeometry(200, 200);
            const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x4a5d23 });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            scene.add(ground);
            
            // Road
            const roadGeometry = new THREE.PlaneGeometry(200, 15);
            const roadMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
            const road = new THREE.Mesh(roadGeometry, roadMaterial);
            road.rotation.x = -Math.PI / 2;
            road.position.y = 0.01;
            scene.add(road);
            
            // Road markings
            const markingGeometry = new THREE.PlaneGeometry(3, 0.4);
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
            
            for (let i = -100; i < 100; i += 10) {
                const marking = new THREE.Mesh(markingGeometry, markingMaterial);
                marking.rotation.x = -Math.PI / 2;
                marking.position.set(i, 0.02, 0);
                scene.add(marking);
            }
            
            // Simple buildings
            createSimpleBuildings();
        }
        
        function createSimpleBuildings() {
            const buildingPositions = [
                { x: 30, z: 30, h: 20 }, { x: -30, z: 30, h: 15 },
                { x: 30, z: -30, h: 25 }, { x: -30, z: -30, h: 18 }
            ];
            
            buildingPositions.forEach(pos => {
                const buildingGeometry = new THREE.BoxGeometry(10, pos.h, 8);
                const buildingMaterial = new THREE.MeshBasicMaterial({ 
                    color: Math.random() * 0x888888 + 0x444444 
                });
                const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
                building.position.set(pos.x, pos.h / 2, pos.z);
                scene.add(building);
            });
        }
        
        function createCar() {
            // Car body
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshBasicMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            scene.add(car);
            
            // Wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 8);
            const wheelMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
            
            const wheelPositions = [
                { x: -1, y: -0.3, z: 1.2 },
                { x: 1, y: -0.3, z: 1.2 },
                { x: -1, y: -0.3, z: -1.2 },
                { x: 1, y: -0.3, z: -1.2 }
            ];
            
            car.wheels = [];
            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                car.add(wheel);
                car.wheels.push(wheel);
            });
            
            console.log('🚗 Mobile car created');
        }
        
        function setupLighting() {
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
        }
        
        function setupMobileControls() {
            const steeringStick = document.getElementById('steeringStick');
            const steeringKnob = document.getElementById('steeringKnob');
            const accelerationStick = document.getElementById('accelerationStick');
            const accelerationKnob = document.getElementById('accelerationKnob');
            
            // Steering stick
            setupJoystick(steeringStick, steeringKnob, (x, y) => {
                steeringInput = x;
                isSteeringActive = Math.abs(x) > 0.1;
            });
            
            // Acceleration stick
            setupJoystick(accelerationStick, accelerationKnob, (x, y) => {
                accelerationInput = -y; // Invert Y for forward/backward
                isAccelerationActive = Math.abs(y) > 0.1;
            });
            
            // Camera button
            document.getElementById('cameraBtn').addEventListener('click', switchCamera);
            
            // Reset button
            document.getElementById('resetBtn').addEventListener('click', resetCar);
            
            // Prevent context menu
            document.addEventListener('contextmenu', e => e.preventDefault());
        }
        
        function setupJoystick(stick, knob, callback) {
            let isDragging = false;
            let startX, startY;
            const stickRect = stick.getBoundingClientRect();
            const stickRadius = stickRect.width / 2;
            const knobRadius = 20;
            
            function handleStart(e) {
                isDragging = true;
                const touch = e.touches ? e.touches[0] : e;
                startX = touch.clientX;
                startY = touch.clientY;
                e.preventDefault();
            }
            
            function handleMove(e) {
                if (!isDragging) return;
                
                const touch = e.touches ? e.touches[0] : e;
                const rect = stick.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                let deltaX = touch.clientX - centerX;
                let deltaY = touch.clientY - centerY;
                
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                const maxDistance = stickRadius - knobRadius;
                
                if (distance > maxDistance) {
                    deltaX = (deltaX / distance) * maxDistance;
                    deltaY = (deltaY / distance) * maxDistance;
                }
                
                knob.style.transform = `translate(calc(-50% + ${deltaX}px), calc(-50% + ${deltaY}px))`;
                
                const normalizedX = deltaX / maxDistance;
                const normalizedY = deltaY / maxDistance;
                
                callback(normalizedX, normalizedY);
                e.preventDefault();
            }
            
            function handleEnd(e) {
                isDragging = false;
                knob.style.transform = 'translate(-50%, -50%)';
                callback(0, 0);
                e.preventDefault();
            }
            
            // Touch events
            stick.addEventListener('touchstart', handleStart);
            document.addEventListener('touchmove', handleMove);
            document.addEventListener('touchend', handleEnd);
            
            // Mouse events for testing on desktop
            stick.addEventListener('mousedown', handleStart);
            document.addEventListener('mousemove', handleMove);
            document.addEventListener('mouseup', handleEnd);
        }
        
        function updateCar() {
            const acceleration = 0.025;
            const maxSpeed = 0.6;
            const friction = 0.96;
            const turnSpeed = 0.03;
            
            // Handle mobile input
            let engineForce = accelerationInput * acceleration;
            let steerAngle = steeringInput * turnSpeed;
            
            // Update velocity
            if (Math.abs(engineForce) > 0.001) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;
            }
            
            // Apply friction
            carVelocity.x *= friction;
            carVelocity.z *= friction;
            
            // Limit speed
            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }
            
            // Update rotation
            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.01) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }
            
            // Update position
            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;
            
            // Update car mesh
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;
            
            // Animate wheels
            if (car.wheels) {
                const wheelRotation = currentSpeed * 5;
                car.wheels.forEach((wheel, index) => {
                    wheel.rotation.x += wheelRotation;
                    if (index < 2) { // Front wheels
                        wheel.rotation.y = steerAngle * 3;
                    }
                });
            }
            
            // Calculate speed for display
            speed = currentSpeed * 100;
            score += currentSpeed * 0.05;
        }
        
        function updateCamera() {
            const smoothness = 0.1;
            let targetX, targetY, targetZ;
            
            switch(cameraMode) {
                case 0: // Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 10;
                    targetY = carPosition.y + 5;
                    targetZ = carPosition.z - Math.cos(carRotation) * 10;
                    break;
                    
                case 1: // First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 0.5;
                    targetY = carPosition.y + 1;
                    targetZ = carPosition.z + Math.cos(carRotation) * 0.5;
                    break;
                    
                case 2: // Top Down
                    targetX = carPosition.x;
                    targetY = carPosition.y + 20;
                    targetZ = carPosition.z;
                    break;
            }
            
            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(carPosition.x, carPosition.y, carPosition.z);
        }
        
        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            document.getElementById('cameraMode').textContent = cameraModes[cameraMode];
            showNotification(`📷 ${cameraModes[cameraMode]}`);
        }
        
        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            showNotification("🔄 Reset");
        }
        
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.getElementById('gameContainer').appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 2000);
        }
        
        function updateUI() {
            gameTime += 1/60;
            const minutes = Math.floor(gameTime / 60);
            const seconds = Math.floor(gameTime % 60);
            
            document.getElementById('speed').textContent = Math.round(speed);
            document.getElementById('score').textContent = Math.round(score);
            document.getElementById('time').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            updateCar();
            updateCamera();
            updateUI();
            
            renderer.render(scene, camera);
        }
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }, 100);
        });
        
        // Start the mobile game
        window.addEventListener('load', () => {
            console.log('📱 Starting Mobile Car Game...');
            init();
        });
    </script>
</body>
</html>
