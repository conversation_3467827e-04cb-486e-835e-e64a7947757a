<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Car Driving Simulator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            overflow: hidden;
        }

        #gameUI {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .hud {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            min-width: 200px;
        }

        .speedometer {
            text-align: center;
            margin-bottom: 1rem;
        }

        .speed-value {
            font-size: 3rem;
            font-weight: bold;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
        }

        .speed-unit {
            font-size: 1rem;
            color: #ccc;
        }

        .info-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            font-size: 0.9rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
        }

        .minimap {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 200px;
            height: 200px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00f5ff;
            border-radius: 15px;
            overflow: hidden;
        }

        .controls-help {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem 2rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            text-align: center;
            font-size: 0.9rem;
        }

        .mission-panel {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #ffaa00;
            min-width: 250px;
        }

        .mission-title {
            color: #ffaa00;
            font-weight: bold;
            margin-bottom: 1rem;
            text-align: center;
        }

        .mission-objective {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: rgba(255, 170, 0, 0.1);
            border-radius: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00f5ff, #0080ff);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .notification {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #00f5ff;
            text-align: center;
            font-size: 1.2rem;
            z-index: 200;
            animation: fadeInOut 3s ease-in-out;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00f5ff;
            border-radius: 50%;
            pointer-events: none;
            animation: particle 2s linear infinite;
        }

        @keyframes particle {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0) translateY(-50px); }
        }

        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="gameUI">
        <!-- HUD -->
        <div class="hud">
            <div class="speedometer">
                <div class="speed-value" id="speedValue">0</div>
                <div class="speed-unit">KM/H</div>
            </div>
            <div class="info-panel">
                <div class="info-item">
                    <span>Score:</span>
                    <span id="score">0</span>
                </div>
                <div class="info-item">
                    <span>Time:</span>
                    <span id="time">0:00</span>
                </div>
                <div class="info-item">
                    <span>Camera:</span>
                    <span id="cameraMode">Third Person</span>
                </div>
                <div class="info-item">
                    <span>Gear:</span>
                    <span id="gear">D</span>
                </div>
            </div>
        </div>

        <!-- Minimap -->
        <div class="minimap">
            <canvas id="minimapCanvas" width="200" height="200"></canvas>
        </div>

        <!-- Mission Panel -->
        <div class="mission-panel">
            <div class="mission-title">🎯 Current Mission</div>
            <div class="mission-objective" id="missionText">
                Drive around and explore the city
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="missionProgress" style="width: 0%"></div>
            </div>
            <div style="text-align: center; margin-top: 1rem; font-size: 0.8rem;">
                Progress: <span id="progressText">0%</span>
            </div>
        </div>

        <!-- Controls Help -->
        <div class="controls-help">
            <strong>🎮 Controls:</strong>
            WASD/Arrows: Drive • C: Camera • R: Reset • Space: Handbrake • M: Mission
        </div>
    </div>

    <canvas id="canvas"></canvas>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Enhanced Game Variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let cameraMode = 0;
        let gameTime = 0;
        let score = 0;
        let currentMission = 0;
        let missionProgress = 0;

        const cameraModes = ['Third Person', 'First Person', 'Top Down', 'Cinematic'];
        const missions = [
            { text: "Drive around and explore the city", target: 100, type: "distance" },
            { text: "Reach a speed of 80 km/h", target: 80, type: "speed" },
            { text: "Drive for 2 minutes", target: 120, type: "time" },
            { text: "Complete 5 laps around the track", target: 5, type: "laps" }
        ];

        // Enhanced Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false,
            Space: false
        };

        // Particle system
        let particles = [];

        // Traffic cars
        let trafficCars = [];

        // Initialize the enhanced game
        function init() {
            console.log('🚗 Initializing Enhanced Car Driving Simulator...');

            // Create scene with fog
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);
            scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

            // Create renderer with enhanced settings
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('canvas'),
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Create enhanced environment
            createEnvironment();
            createCar();
            createTrafficCars();
            setupLighting();
            setupEventListeners();

            // Initialize minimap
            initMinimap();

            // Start game loop
            animate();

            console.log('✅ Enhanced game initialized successfully!');
        }

        function createEnvironment() {
            // Ground with texture-like appearance
            const groundGeometry = new THREE.PlaneGeometry(400, 400);
            const groundMaterial = new THREE.MeshLambertMaterial({
                color: 0x4a5d23,
                transparent: true,
                opacity: 0.8
            });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Main road network
            createRoadNetwork();

            // Buildings
            createBuildings();

            // Trees and scenery
            createScenery();
        }

        function createRoadNetwork() {
            // Main highway
            const mainRoadGeometry = new THREE.PlaneGeometry(400, 20);
            const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const mainRoad = new THREE.Mesh(mainRoadGeometry, roadMaterial);
            mainRoad.rotation.x = -Math.PI / 2;
            mainRoad.position.y = 0.01;
            mainRoad.receiveShadow = true;
            scene.add(mainRoad);

            // Cross roads
            const crossRoadGeometry = new THREE.PlaneGeometry(20, 400);
            const crossRoad = new THREE.Mesh(crossRoadGeometry, roadMaterial);
            crossRoad.rotation.x = -Math.PI / 2;
            crossRoad.position.y = 0.01;
            crossRoad.receiveShadow = true;
            scene.add(crossRoad);

            // Road markings
            createRoadMarkings();
        }

        function createRoadMarkings() {
            const markingGeometry = new THREE.PlaneGeometry(4, 0.5);
            const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

            // Center line markings
            for (let i = -200; i < 200; i += 15) {
                const marking = new THREE.Mesh(markingGeometry, markingMaterial);
                marking.rotation.x = -Math.PI / 2;
                marking.position.set(i, 0.02, 0);
                scene.add(marking);

                const crossMarking = new THREE.Mesh(markingGeometry, markingMaterial);
                crossMarking.rotation.x = -Math.PI / 2;
                crossMarking.rotation.z = Math.PI / 2;
                crossMarking.position.set(0, 0.02, i);
                scene.add(crossMarking);
            }
        }

        function createBuildings() {
            const buildingConfigs = [
                { x: 40, z: 40, w: 15, h: 25, d: 12, color: 0x8B4513 },
                { x: -40, z: 40, w: 12, h: 20, d: 15, color: 0x696969 },
                { x: 60, z: -30, w: 18, h: 30, d: 14, color: 0x4682B4 },
                { x: -60, z: -30, w: 14, h: 22, d: 16, color: 0x8B0000 },
                { x: 80, z: 60, w: 20, h: 35, d: 18, color: 0x2F4F4F },
                { x: -80, z: 60, w: 16, h: 28, d: 14, color: 0x800080 }
            ];

            buildingConfigs.forEach(config => {
                const buildingGeometry = new THREE.BoxGeometry(config.w, config.h, config.d);
                const buildingMaterial = new THREE.MeshLambertMaterial({ color: config.color });
                const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
                building.position.set(config.x, config.h / 2, config.z);
                building.castShadow = true;
                building.receiveShadow = true;
                scene.add(building);

                // Add windows
                addWindows(building, config);
            });
        }

        function addWindows(building, config) {
            const windowGeometry = new THREE.PlaneGeometry(1.5, 1.5);
            const windowMaterial = new THREE.MeshBasicMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7
            });

            // Front face windows
            for (let i = -config.w/2 + 2; i < config.w/2; i += 3) {
                for (let j = 2; j < config.h - 2; j += 4) {
                    const window = new THREE.Mesh(windowGeometry, windowMaterial);
                    window.position.set(i, j - config.h/2, config.d/2 + 0.1);
                    building.add(window);
                }
            }
        }

        function createScenery() {
            // Trees
            const treePositions = [
                { x: 25, z: 25 }, { x: -25, z: 25 }, { x: 25, z: -25 }, { x: -25, z: -25 },
                { x: 45, z: 15 }, { x: -45, z: 15 }, { x: 45, z: -15 }, { x: -45, z: -15 }
            ];

            treePositions.forEach(pos => {
                createTree(pos.x, pos.z);
            });

            // Street lights
            createStreetLights();
        }

        function createTree(x, z) {
            // Trunk
            const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 6);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(x, 3, z);
            trunk.castShadow = true;
            scene.add(trunk);

            // Leaves
            const leavesGeometry = new THREE.SphereGeometry(3, 8, 8);
            const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
            leaves.position.set(x, 8, z);
            leaves.castShadow = true;
            scene.add(leaves);
        }

        function createStreetLights() {
            const lightPositions = [
                { x: 15, z: 30 }, { x: -15, z: 30 }, { x: 15, z: -30 }, { x: -15, z: -30 },
                { x: 30, z: 15 }, { x: 30, z: -15 }, { x: -30, z: 15 }, { x: -30, z: -15 }
            ];

            lightPositions.forEach(pos => {
                // Pole
                const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 8);
                const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
                const pole = new THREE.Mesh(poleGeometry, poleMaterial);
                pole.position.set(pos.x, 4, pos.z);
                pole.castShadow = true;
                scene.add(pole);

                // Light
                const lightGeometry = new THREE.SphereGeometry(0.5, 8, 8);
                const lightMaterial = new THREE.MeshBasicMaterial({
                    color: 0xffffaa,
                    emissive: 0x444400
                });
                const light = new THREE.Mesh(lightGeometry, lightMaterial);
                light.position.set(pos.x, 7.5, pos.z);
                scene.add(light);

                // Point light
                const pointLight = new THREE.PointLight(0xffffaa, 0.5, 30);
                pointLight.position.set(pos.x, 7.5, pos.z);
                pointLight.castShadow = true;
                scene.add(pointLight);
            });
        }

        function createCar() {
            // Enhanced car with better details
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshLambertMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.castShadow = true;
            scene.add(car);

            // Car details
            addCarDetails();

            console.log('🚗 Enhanced car created');
        }

        function addCarDetails() {
            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(1.8, 0.8, 0.1);
            const windshieldMaterial = new THREE.MeshLambertMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.6
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.2, 1.5);
            car.add(windshield);

            // Headlights
            const headlightGeometry = new THREE.SphereGeometry(0.2, 8, 8);
            const headlightMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffaa,
                emissive: 0x222200
            });

            const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
            leftHeadlight.position.set(-0.7, 0, 2);
            car.add(leftHeadlight);

            const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
            rightHeadlight.position.set(0.7, 0, 2);
            car.add(rightHeadlight);

            // Taillights
            const taillightMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                emissive: 0x220000
            });

            const leftTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
            leftTaillight.position.set(-0.7, 0, -2);
            car.add(leftTaillight);

            const rightTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
            rightTaillight.position.set(0.7, 0, -2);
            car.add(rightTaillight);

            // Wheels with rotation
            createWheels();
        }

        function createWheels() {
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 12);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            const wheelPositions = [
                { x: -1, y: -0.3, z: 1.2 },   // Front left
                { x: 1, y: -0.3, z: 1.2 },    // Front right
                { x: -1, y: -0.3, z: -1.2 },  // Rear left
                { x: 1, y: -0.3, z: -1.2 }    // Rear right
            ];

            car.wheels = [];
            wheelPositions.forEach((pos, index) => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                wheel.castShadow = true;
                car.add(wheel);
                car.wheels.push(wheel);
            });
        }

        function createTrafficCars() {
            const trafficPositions = [
                { x: 50, z: 0, rotation: 0 },
                { x: -50, z: 0, rotation: Math.PI },
                { x: 0, z: 50, rotation: -Math.PI/2 },
                { x: 0, z: -50, rotation: Math.PI/2 }
            ];

            trafficPositions.forEach(pos => {
                const trafficCar = createTrafficCar(pos.x, pos.z, pos.rotation);
                trafficCars.push(trafficCar);
                scene.add(trafficCar);
            });
        }

        function createTrafficCar(x, z, rotation) {
            const colors = [0x0066cc, 0x00cc66, 0xcc6600, 0x6600cc, 0xcc0066];
            const color = colors[Math.floor(Math.random() * colors.length)];

            const carGeometry = new THREE.BoxGeometry(1.8, 0.9, 3.5);
            const carMaterial = new THREE.MeshLambertMaterial({ color: color });
            const trafficCar = new THREE.Mesh(carGeometry, carMaterial);

            trafficCar.position.set(x, 0.9, z);
            trafficCar.rotation.y = rotation;
            trafficCar.castShadow = true;

            // Add simple wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2, 8);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

            const wheelPositions = [
                { x: -0.8, y: -0.3, z: 1 },
                { x: 0.8, y: -0.3, z: 1 },
                { x: -0.8, y: -0.3, z: -1 },
                { x: 0.8, y: -0.3, z: -1 }
            ];

            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                trafficCar.add(wheel);
            });

            return trafficCar;
        }

        function setupLighting() {
            // Enhanced lighting system
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 25);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -100;
            directionalLight.shadow.camera.right = 100;
            directionalLight.shadow.camera.top = 100;
            directionalLight.shadow.camera.bottom = -100;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 200;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Hemisphere light for natural lighting
            const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x362d1d, 0.3);
            scene.add(hemisphereLight);
        }

        function setupEventListeners() {
            // Enhanced keyboard events
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code === 'Space' ? 'Space' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }

                // Special keys
                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }
                if (event.code === 'KeyM') {
                    nextMission();
                }

                event.preventDefault();
            });

            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code === 'Space' ? 'Space' :
                           event.code;

                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });

            // Window resize
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function updateCar() {
            const acceleration = 0.03;
            const maxSpeed = 0.8;
            const friction = keys.Space ? 0.85 : 0.96; // Handbrake
            const turnSpeed = 0.04;

            // Handle input
            let engineForce = 0;
            let steerAngle = 0;

            if (keys.w || keys.ArrowUp) engineForce = acceleration;
            if (keys.s || keys.ArrowDown) engineForce = -acceleration * 0.6;
            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;

            // Update velocity
            if (engineForce !== 0) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;

                // Create speed particles
                if (Math.random() < 0.3) {
                    createSpeedParticle();
                }
            }

            // Apply friction
            carVelocity.x *= friction;
            carVelocity.z *= friction;

            // Limit speed
            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }

            // Update rotation
            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.02) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }

            // Update position
            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;

            // Update car mesh
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;

            // Animate wheels
            if (car.wheels) {
                const wheelRotation = currentSpeed * 5;
                car.wheels.forEach((wheel, index) => {
                    wheel.rotation.x += wheelRotation;
                    // Front wheels steering
                    if (index < 2) {
                        wheel.rotation.y = steerAngle * 2;
                    }
                });
            }

            // Calculate speed for display
            speed = currentSpeed * 120; // Convert to km/h scale

            // Update score
            score += currentSpeed * 0.1;
        }

        function updateCamera() {
            const smoothness = 0.1;
            let targetX, targetY, targetZ, lookAtX, lookAtY, lookAtZ;

            switch(cameraMode) {
                case 0: // Third Person
                    targetX = carPosition.x - Math.sin(carRotation) * 12;
                    targetY = carPosition.y + 6;
                    targetZ = carPosition.z - Math.cos(carRotation) * 12;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y + 1;
                    lookAtZ = carPosition.z;
                    break;

                case 1: // First Person
                    targetX = carPosition.x + Math.sin(carRotation) * 0.5;
                    targetY = carPosition.y + 1.2;
                    targetZ = carPosition.z + Math.cos(carRotation) * 0.5;
                    lookAtX = carPosition.x + Math.sin(carRotation) * 10;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z + Math.cos(carRotation) * 10;
                    break;

                case 2: // Top Down
                    targetX = carPosition.x;
                    targetY = carPosition.y + 25;
                    targetZ = carPosition.z;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y;
                    lookAtZ = carPosition.z;
                    break;

                case 3: // Cinematic
                    const time = Date.now() * 0.001;
                    const radius = 15;
                    targetX = carPosition.x + Math.cos(time * 0.5) * radius;
                    targetY = carPosition.y + 8 + Math.sin(time * 0.3) * 3;
                    targetZ = carPosition.z + Math.sin(time * 0.5) * radius;
                    lookAtX = carPosition.x;
                    lookAtY = carPosition.y + 1;
                    lookAtZ = carPosition.z;
                    break;
            }

            // Smooth camera movement
            camera.position.x += (targetX - camera.position.x) * smoothness;
            camera.position.y += (targetY - camera.position.y) * smoothness;
            camera.position.z += (targetZ - camera.position.z) * smoothness;
            camera.lookAt(lookAtX, lookAtY, lookAtZ);
        }

        function updateTraffic() {
            trafficCars.forEach(trafficCar => {
                // Simple AI movement
                const moveSpeed = 0.02;
                trafficCar.position.x += Math.sin(trafficCar.rotation.y) * moveSpeed;
                trafficCar.position.z += Math.cos(trafficCar.rotation.y) * moveSpeed;

                // Reset position if too far
                if (Math.abs(trafficCar.position.x) > 100 || Math.abs(trafficCar.position.z) > 100) {
                    trafficCar.position.x = (Math.random() - 0.5) * 20;
                    trafficCar.position.z = (Math.random() - 0.5) * 20;
                }
            });
        }

        function updateMissions() {
            const mission = missions[currentMission];
            let progress = 0;

            switch(mission.type) {
                case "distance":
                    progress = Math.min(score / mission.target, 1);
                    break;
                case "speed":
                    progress = Math.min(speed / mission.target, 1);
                    break;
                case "time":
                    progress = Math.min(gameTime / mission.target, 1);
                    break;
                case "laps":
                    // Simple lap detection based on position
                    progress = Math.min((Math.abs(carPosition.x) + Math.abs(carPosition.z)) / (mission.target * 100), 1);
                    break;
            }

            missionProgress = progress * 100;

            // Check mission completion
            if (progress >= 1 && missionProgress >= 99) {
                completeMission();
            }
        }

        function completeMission() {
            showNotification("🎉 Mission Complete!");
            score += 1000;
            nextMission();
        }

        function nextMission() {
            currentMission = (currentMission + 1) % missions.length;
            missionProgress = 0;
            updateMissionUI();
        }

        function updateMissionUI() {
            document.getElementById('missionText').textContent = missions[currentMission].text;
            document.getElementById('missionProgress').style.width = missionProgress + '%';
            document.getElementById('progressText').textContent = Math.round(missionProgress) + '%';
        }

        function createSpeedParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = (Math.random() * window.innerWidth) + 'px';
            particle.style.top = (Math.random() * window.innerHeight) + 'px';
            document.body.appendChild(particle);

            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 2000);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.getElementById('gameUI').appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            document.getElementById('cameraMode').textContent = cameraModes[cameraMode];
            showNotification(`📷 ${cameraModes[cameraMode]} Camera`);
        }

        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            showNotification("🔄 Car Reset");
        }

        function initMinimap() {
            const minimapCanvas = document.getElementById('minimapCanvas');
            const ctx = minimapCanvas.getContext('2d');

            // Draw minimap background
            ctx.fillStyle = '#2a5d23';
            ctx.fillRect(0, 0, 200, 200);

            // Draw roads
            ctx.fillStyle = '#333333';
            ctx.fillRect(90, 0, 20, 200); // Vertical road
            ctx.fillRect(0, 90, 200, 20); // Horizontal road
        }

        function updateMinimap() {
            const minimapCanvas = document.getElementById('minimapCanvas');
            const ctx = minimapCanvas.getContext('2d');

            // Clear and redraw background
            ctx.fillStyle = '#2a5d23';
            ctx.fillRect(0, 0, 200, 200);

            // Draw roads
            ctx.fillStyle = '#333333';
            ctx.fillRect(90, 0, 20, 200);
            ctx.fillRect(0, 90, 200, 20);

            // Draw player car
            const mapX = 100 + (carPosition.x / 2);
            const mapZ = 100 + (carPosition.z / 2);

            ctx.fillStyle = '#ff4444';
            ctx.beginPath();
            ctx.arc(mapX, mapZ, 3, 0, 2 * Math.PI);
            ctx.fill();

            // Draw traffic cars
            ctx.fillStyle = '#0066cc';
            trafficCars.forEach(trafficCar => {
                const tMapX = 100 + (trafficCar.position.x / 2);
                const tMapZ = 100 + (trafficCar.position.z / 2);
                ctx.beginPath();
                ctx.arc(tMapX, tMapZ, 2, 0, 2 * Math.PI);
                ctx.fill();
            });
        }

        function updateUI() {
            // Update time
            gameTime += 1/60; // Assuming 60 FPS
            const minutes = Math.floor(gameTime / 60);
            const seconds = Math.floor(gameTime % 60);
            document.getElementById('time').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

            // Update other UI elements
            document.getElementById('speedValue').textContent = Math.round(speed);
            document.getElementById('score').textContent = Math.round(score);
            document.getElementById('gear').textContent = speed > 5 ? 'D' : 'P';

            // Update mission progress
            updateMissions();
            updateMissionUI();

            // Update minimap
            updateMinimap();
        }

        function animate() {
            requestAnimationFrame(animate);

            updateCar();
            updateCamera();
            updateTraffic();
            updateUI();

            renderer.render(scene, camera);
        }

        // Start the enhanced game
        window.addEventListener('load', () => {
            console.log('🚀 Starting Enhanced Car Driving Simulator...');
            init();
        });
    </script>
</body>
</html>
