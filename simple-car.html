<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Car Driving</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Courier New', monospace;
            color: white;
            overflow: hidden;
        }
        
        #ui {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            z-index: 100;
        }
        
        #controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            text-align: center;
        }
        
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="ui">
        <h3>🚗 Car Driving Simulator</h3>
        <div>Speed: <span id="speed">0</span> km/h</div>
        <div>Camera: <span id="camera-mode">Third Person</span></div>
        <div>Position: <span id="position">0, 0</span></div>
    </div>
    
    <div id="controls">
        <strong>Controls:</strong> WASD to drive • C to change camera • R to reset
    </div>
    
    <canvas id="canvas"></canvas>
    
    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        // Game variables
        let scene, camera, renderer, car, ground;
        let carPosition = { x: 0, y: 1, z: 0 };
        let carRotation = 0;
        let carVelocity = { x: 0, z: 0 };
        let speed = 0;
        let cameraMode = 0;
        const cameraModes = ['Third Person', 'First Person', 'Top Down'];
        
        // Input handling
        const keys = {
            w: false, a: false, s: false, d: false,
            ArrowUp: false, ArrowLeft: false, ArrowDown: false, ArrowRight: false
        };
        
        // Initialize the game
        function init() {
            console.log('🚗 Initializing Simple Car Game...');
            
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB); // Sky blue
            
            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            
            // Create renderer
            renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('canvas'), antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            
            // Create ground
            const groundGeometry = new THREE.PlaneGeometry(200, 200);
            const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x4a5d23 });
            ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            scene.add(ground);
            
            // Create road
            const roadGeometry = new THREE.PlaneGeometry(200, 10);
            const roadMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
            const road = new THREE.Mesh(roadGeometry, roadMaterial);
            road.rotation.x = -Math.PI / 2;
            road.position.y = 0.01;
            scene.add(road);
            
            // Create car
            createCar();
            
            // Setup lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // Setup camera
            updateCamera();
            
            // Setup event listeners
            setupEventListeners();
            
            // Start game loop
            animate();
            
            console.log('✅ Game initialized successfully!');
        }
        
        function createCar() {
            // Car body
            const carGeometry = new THREE.BoxGeometry(2, 1, 4);
            const carMaterial = new THREE.MeshBasicMaterial({ color: 0xff4444 });
            car = new THREE.Mesh(carGeometry, carMaterial);
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            scene.add(car);
            
            // Car details
            // Windshield
            const windshieldGeometry = new THREE.BoxGeometry(1.8, 0.8, 0.1);
            const windshieldMaterial = new THREE.MeshBasicMaterial({ 
                color: 0x87CEEB, 
                transparent: true, 
                opacity: 0.5 
            });
            const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
            windshield.position.set(0, 0.2, 1.5);
            car.add(windshield);
            
            // Wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 8);
            const wheelMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
            
            const wheelPositions = [
                { x: -1, y: -0.3, z: 1.2 },   // Front left
                { x: 1, y: -0.3, z: 1.2 },    // Front right
                { x: -1, y: -0.3, z: -1.2 },  // Rear left
                { x: 1, y: -0.3, z: -1.2 }    // Rear right
            ];
            
            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.position.set(pos.x, pos.y, pos.z);
                car.add(wheel);
            });
            
            console.log('🚗 Car created at position:', car.position);
        }
        
        function setupEventListeners() {
            // Keyboard events
            document.addEventListener('keydown', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;
                
                if (keys.hasOwnProperty(key)) {
                    keys[key] = true;
                }
                
                // Special keys
                if (event.code === 'KeyC') {
                    switchCamera();
                }
                if (event.code === 'KeyR') {
                    resetCar();
                }
            });
            
            document.addEventListener('keyup', (event) => {
                const key = event.code === 'KeyW' ? 'w' :
                           event.code === 'KeyA' ? 'a' :
                           event.code === 'KeyS' ? 's' :
                           event.code === 'KeyD' ? 'd' :
                           event.code;
                
                if (keys.hasOwnProperty(key)) {
                    keys[key] = false;
                }
            });
            
            // Window resize
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }
        
        function updateCar() {
            const acceleration = 0.02;
            const maxSpeed = 0.5;
            const friction = 0.95;
            const turnSpeed = 0.03;
            
            // Handle input
            let engineForce = 0;
            let steerAngle = 0;
            
            if (keys.w || keys.ArrowUp) engineForce = acceleration;
            if (keys.s || keys.ArrowDown) engineForce = -acceleration * 0.5;
            if (keys.a || keys.ArrowLeft) steerAngle = turnSpeed;
            if (keys.d || keys.ArrowRight) steerAngle = -turnSpeed;
            
            // Update velocity
            if (engineForce !== 0) {
                carVelocity.x += Math.sin(carRotation) * engineForce;
                carVelocity.z += Math.cos(carRotation) * engineForce;
            }
            
            // Apply friction
            carVelocity.x *= friction;
            carVelocity.z *= friction;
            
            // Limit speed
            const currentSpeed = Math.sqrt(carVelocity.x * carVelocity.x + carVelocity.z * carVelocity.z);
            if (currentSpeed > maxSpeed) {
                carVelocity.x = (carVelocity.x / currentSpeed) * maxSpeed;
                carVelocity.z = (carVelocity.z / currentSpeed) * maxSpeed;
            }
            
            // Update rotation
            if (Math.abs(steerAngle) > 0 && currentSpeed > 0.01) {
                carRotation += steerAngle * (currentSpeed / maxSpeed);
            }
            
            // Update position
            carPosition.x += carVelocity.x;
            carPosition.z += carVelocity.z;
            
            // Update car mesh
            car.position.set(carPosition.x, carPosition.y, carPosition.z);
            car.rotation.y = carRotation;
            
            // Calculate speed for display
            speed = currentSpeed * 100; // Convert to km/h scale
        }
        
        function updateCamera() {
            switch(cameraMode) {
                case 0: // Third Person
                    camera.position.x = carPosition.x - Math.sin(carRotation) * 10;
                    camera.position.y = carPosition.y + 5;
                    camera.position.z = carPosition.z - Math.cos(carRotation) * 10;
                    camera.lookAt(carPosition.x, carPosition.y, carPosition.z);
                    break;
                    
                case 1: // First Person
                    camera.position.x = carPosition.x + Math.sin(carRotation) * 1;
                    camera.position.y = carPosition.y + 1.5;
                    camera.position.z = carPosition.z + Math.cos(carRotation) * 1;
                    camera.lookAt(
                        carPosition.x + Math.sin(carRotation) * 10,
                        carPosition.y,
                        carPosition.z + Math.cos(carRotation) * 10
                    );
                    break;
                    
                case 2: // Top Down
                    camera.position.x = carPosition.x;
                    camera.position.y = carPosition.y + 20;
                    camera.position.z = carPosition.z;
                    camera.lookAt(carPosition.x, carPosition.y, carPosition.z);
                    break;
            }
        }
        
        function switchCamera() {
            cameraMode = (cameraMode + 1) % cameraModes.length;
            document.getElementById('camera-mode').textContent = cameraModes[cameraMode];
            console.log('📷 Camera switched to:', cameraModes[cameraMode]);
        }
        
        function resetCar() {
            carPosition = { x: 0, y: 1, z: 0 };
            carRotation = 0;
            carVelocity = { x: 0, z: 0 };
            speed = 0;
            console.log('🔄 Car reset');
        }
        
        function updateUI() {
            document.getElementById('speed').textContent = Math.round(speed);
            document.getElementById('position').textContent = 
                `${carPosition.x.toFixed(1)}, ${carPosition.z.toFixed(1)}`;
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            updateCar();
            updateCamera();
            updateUI();
            
            renderer.render(scene, camera);
        }
        
        // Start the game when page loads
        window.addEventListener('load', () => {
            console.log('🚀 Starting Simple Car Game...');
            init();
        });
    </script>
</body>
</html>
