# 🚗 Car Driving Simulator

A modern, web-based 3D car driving application inspired by Dr. Driving, built with Three.js and Cannon.js physics engine.

## ✨ Features

### 🎮 Gameplay
- **Realistic 3D Car Physics** - Authentic driving experience with proper acceleration, steering, and braking
- **Multiple Camera Views** - Third-person, first-person, top-down, and cinematic camera modes
- **Dynamic Environment** - City environment with roads, buildings, trees, and street lights
- **Smooth Controls** - Responsive WASD/Arrow key controls with realistic car handling
- **Real-time HUD** - Speed meter, score display, and camera mode indicator

### 🌟 Visual Features
- **Modern 3D Graphics** - WebGL-powered rendering with shadows and lighting
- **Professional UI Design** - Clean, futuristic interface with smooth animations
- **Responsive Design** - Works on different screen sizes and devices
- **Loading Screen** - Animated loading sequence with progress indication
- **Visual Effects** - Fog, dynamic lighting, and particle effects

### 🎯 Technical Features
- **Physics Simulation** - Realistic car physics using Cannon.js
- **Performance Optimized** - Smooth 60 FPS gameplay
- **Modular Architecture** - Clean, maintainable code structure
- **Error Handling** - Robust error handling and user feedback

## 🚀 Quick Start

### Option 1: Direct Play (Recommended)
1. **Download** all files to a folder on your computer
2. **Double-click** `index.html` to open in your browser
3. **Click "Start Driving"** and enjoy!

### Option 2: Local Server (For Development)
```bash
# If you have Python installed
python -m http.server 8000

# If you have Node.js installed
npx serve .

# Then open http://localhost:8000 in your browser
```

## 🎮 Controls

| Key | Action |
|-----|--------|
| **W** / **↑** | Accelerate |
| **S** / **↓** | Brake / Reverse |
| **A** / **←** | Turn Left |
| **D** / **→** | Turn Right |
| **C** | Change Camera View |
| **R** | Reset Car Position |
| **ESC** | Pause / Resume Game |

## 📱 Camera Modes

- **Third Person** - Classic behind-the-car view (default)
- **First Person** - Driver's seat perspective
- **Top Down** - Bird's eye view for navigation
- **Cinematic** - Dynamic rotating camera for screenshots

## 🛠️ System Requirements

### Minimum Requirements
- **Browser**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **WebGL**: WebGL 1.0 support required
- **RAM**: 2GB available memory
- **Graphics**: Integrated graphics sufficient

### Recommended Requirements
- **Browser**: Latest version of Chrome or Firefox
- **WebGL**: WebGL 2.0 support
- **RAM**: 4GB+ available memory
- **Graphics**: Dedicated graphics card for best performance

## 🏗️ Project Structure

```
car-driving-app/
├── index.html              # Main HTML file
├── styles.css              # CSS styling and animations
├── js/
│   ├── main.js             # Application entry point
│   ├── GameEngine.js       # Core game engine
│   ├── CarController.js    # Car physics and controls
│   ├── Environment.js      # World generation
│   ├── CameraController.js # Camera management
│   └── UI.js              # User interface controller
└── README.md              # This file
```

## 🔧 Development

### Adding New Features
The modular architecture makes it easy to extend:

- **New Car Models**: Modify `CarController.js`
- **Environment Objects**: Add to `Environment.js`
- **UI Elements**: Update `UI.js` and `styles.css`
- **Game Mechanics**: Extend `GameEngine.js`

### Debug Mode
Add `?debug=true` to the URL to enable:
- FPS counter
- Console logging
- Performance metrics

Example: `file:///path/to/index.html?debug=true`

## 🎨 Customization

### Changing Car Color
Edit the car material in `CarController.js`:
```javascript
const carMaterial = new THREE.MeshLambertMaterial({ 
    color: 0xff4444  // Change this hex color
});
```

### Modifying Environment
Adjust building colors, positions, and sizes in `Environment.js`:
```javascript
const buildingConfigs = [
    { x: 50, z: 50, width: 20, height: 30, depth: 15, color: 0x8B4513 }
    // Add more buildings here
];
```

### UI Styling
Customize the interface by editing `styles.css`:
- Colors: Search for `#00f5ff` (cyan) and replace
- Fonts: Change the `font-family` properties
- Layout: Modify the flexbox and grid layouts

## 🐛 Troubleshooting

### Game Won't Load
- **Check Browser Compatibility**: Ensure WebGL is supported
- **Clear Cache**: Refresh with Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
- **Check Console**: Open browser dev tools (F12) for error messages

### Poor Performance
- **Close Other Tabs**: Free up system resources
- **Lower Quality**: Reduce browser zoom level
- **Update Drivers**: Ensure graphics drivers are current

### Controls Not Working
- **Click on Game**: Ensure the game window has focus
- **Check Keyboard**: Try different keys to isolate the issue
- **Restart Game**: Use the restart button or refresh the page

## 🔮 Future Enhancements

### Planned Features
- **Multiple Car Models** - Sports cars, trucks, motorcycles
- **Traffic System** - AI-controlled vehicles
- **Missions** - Parking challenges, time trials, delivery tasks
- **Multiplayer** - Online racing with friends
- **Mobile Support** - Touch controls for smartphones
- **Sound Effects** - Engine sounds, music, ambient audio
- **Weather System** - Rain, snow, day/night cycle

### Contributing
This is a learning project, but suggestions are welcome:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Three.js** - 3D graphics library
- **Cannon.js** - Physics engine
- **Google Fonts** - Orbitron font family
- **Dr. Driving** - Inspiration for the concept

## 📞 Support

If you encounter issues or have questions:
1. Check the troubleshooting section above
2. Look for similar issues in browser console
3. Try the debug mode for more information

---

**Enjoy your driving experience! 🚗💨**

*Built with ❤️ using modern web technologies*
