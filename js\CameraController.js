/**
 * Camera Controller Class
 * Manages different camera views and smooth transitions
 */
class CameraController {
    constructor(camera, car) {
        this.camera = camera;
        this.car = car;

        // Camera modes
        this.modes = {
            THIRD_PERSON: 'Third Person',
            FIRST_PERSON: 'First Person',
            TOP_DOWN: 'Top Down',
            CINEMATIC: 'Cinematic'
        };

        this.currentMode = this.modes.THIRD_PERSON;
        this.modeIndex = 0;
        this.modeKeys = Object.keys(this.modes);

        // Camera settings for each mode
        this.cameraSettings = {
            [this.modes.THIRD_PERSON]: {
                offset: { x: 0, y: 8, z: -15 },
                lookAtOffset: { x: 0, y: 2, z: 5 },
                fov: 75,
                smoothness: 0.1
            },
            [this.modes.FIRST_PERSON]: {
                offset: { x: 0, y: 3, z: 2 },
                lookAtOffset: { x: 0, y: 2, z: 20 },
                fov: 90,
                smoothness: 0.15
            },
            [this.modes.TOP_DOWN]: {
                offset: { x: 0, y: 50, z: 0 },
                lookAtOffset: { x: 0, y: 0, z: 0 },
                fov: 60,
                smoothness: 0.08
            },
            [this.modes.CINEMATIC]: {
                offset: { x: 20, y: 10, z: -10 },
                lookAtOffset: { x: 0, y: 2, z: 10 },
                fov: 70,
                smoothness: 0.05
            }
        };

        // Current camera state
        this.targetPosition = new THREE.Vector3();
        this.targetLookAt = new THREE.Vector3();
        this.currentLookAt = new THREE.Vector3();

        // Cinematic mode variables
        this.cinematicTime = 0;
        this.cinematicRadius = 25;

        this.init();
    }

    /**
     * Initialize camera controller
     */
    init() {
        this.updateCameraMode();

        // Set initial camera position
        this.camera.position.set(0, 8, -15);
        this.camera.lookAt(0, 2, 0);

        console.log('📷 Camera controller initialized at position:', this.camera.position);
    }

    /**
     * Switch to next camera mode
     */
    switchCamera() {
        this.modeIndex = (this.modeIndex + 1) % this.modeKeys.length;
        this.currentMode = this.modes[this.modeKeys[this.modeIndex]];
        this.updateCameraMode();

        // Update UI
        const cameraModeElement = document.getElementById('camera-mode');
        if (cameraModeElement) {
            cameraModeElement.textContent = this.currentMode;
        }

        console.log(`📷 Camera switched to: ${this.currentMode}`);
    }

    /**
     * Update camera mode settings
     */
    updateCameraMode() {
        const settings = this.cameraSettings[this.currentMode];

        // Update FOV with smooth transition
        this.targetFOV = settings.fov;

        // Update UI
        const cameraModeElement = document.getElementById('camera-mode');
        if (cameraModeElement) {
            cameraModeElement.textContent = this.currentMode;
        }
    }

    /**
     * Update camera position and rotation
     */
    update(deltaTime) {
        if (!this.car || !this.car.body) return;

        const carPosition = this.car.getPosition();
        const carRotation = this.car.body.quaternion;
        const settings = this.cameraSettings[this.currentMode];

        // Calculate target position and look-at based on current mode
        this.calculateTargetPosition(carPosition, carRotation, settings);

        // Smooth camera movement
        this.smoothCameraMovement(settings.smoothness);

        // Update FOV smoothly
        this.updateFOV();

        // Special handling for cinematic mode
        if (this.currentMode === this.modes.CINEMATIC) {
            this.updateCinematicMode(deltaTime, carPosition);
        }
    }

    /**
     * Calculate target camera position and look-at point
     */
    calculateTargetPosition(carPosition, carRotation, settings) {
        // Create rotation matrix from car quaternion
        const rotationMatrix = new THREE.Matrix4();
        rotationMatrix.makeRotationFromQuaternion(carRotation);

        // Calculate offset in car's local space
        const localOffset = new THREE.Vector3(
            settings.offset.x,
            settings.offset.y,
            settings.offset.z
        );

        const localLookAtOffset = new THREE.Vector3(
            settings.lookAtOffset.x,
            settings.lookAtOffset.y,
            settings.lookAtOffset.z
        );

        // Transform to world space
        localOffset.applyMatrix4(rotationMatrix);
        localLookAtOffset.applyMatrix4(rotationMatrix);

        // Set target position
        this.targetPosition.copy(carPosition).add(localOffset);
        this.targetLookAt.copy(carPosition).add(localLookAtOffset);

        // Special adjustments for different modes
        switch (this.currentMode) {
            case this.modes.TOP_DOWN:
                this.targetPosition.x = carPosition.x;
                this.targetPosition.z = carPosition.z;
                this.targetLookAt.copy(carPosition);
                break;

            case this.modes.FIRST_PERSON:
                // Adjust for car interior view
                this.targetPosition.y = carPosition.y + 2;
                break;
        }
    }

    /**
     * Apply smooth camera movement
     */
    smoothCameraMovement(smoothness) {
        // Smooth position interpolation
        this.camera.position.lerp(this.targetPosition, smoothness);

        // Smooth look-at interpolation
        this.currentLookAt.lerp(this.targetLookAt, smoothness);
        this.camera.lookAt(this.currentLookAt);
    }

    /**
     * Update camera FOV smoothly
     */
    updateFOV() {
        if (this.targetFOV && Math.abs(this.camera.fov - this.targetFOV) > 0.1) {
            this.camera.fov += (this.targetFOV - this.camera.fov) * 0.1;
            this.camera.updateProjectionMatrix();
        }
    }

    /**
     * Special update for cinematic mode
     */
    updateCinematicMode(deltaTime, carPosition) {
        this.cinematicTime += deltaTime;

        // Circular motion around the car
        const angle = this.cinematicTime * 0.5; // Rotation speed
        const x = carPosition.x + Math.cos(angle) * this.cinematicRadius;
        const z = carPosition.z + Math.sin(angle) * this.cinematicRadius;
        const y = carPosition.y + 10 + Math.sin(this.cinematicTime * 2) * 3; // Vertical oscillation

        this.targetPosition.set(x, y, z);
        this.targetLookAt.copy(carPosition);
        this.targetLookAt.y += 2; // Look slightly above the car
    }

    /**
     * Get camera shake effect (for collisions, etc.)
     */
    addCameraShake(intensity = 1, duration = 0.5) {
        const originalPosition = this.camera.position.clone();
        const shakeStart = Date.now();

        const shake = () => {
            const elapsed = (Date.now() - shakeStart) / 1000;
            if (elapsed < duration) {
                const shakeAmount = intensity * (1 - elapsed / duration);
                this.camera.position.x = originalPosition.x + (Math.random() - 0.5) * shakeAmount;
                this.camera.position.y = originalPosition.y + (Math.random() - 0.5) * shakeAmount;
                this.camera.position.z = originalPosition.z + (Math.random() - 0.5) * shakeAmount;

                requestAnimationFrame(shake);
            } else {
                this.camera.position.copy(originalPosition);
            }
        };

        shake();
    }

    /**
     * Set specific camera mode
     */
    setCameraMode(mode) {
        if (this.modes[mode]) {
            this.currentMode = this.modes[mode];
            this.modeIndex = this.modeKeys.indexOf(mode);
            this.updateCameraMode();
        }
    }

    /**
     * Get current camera mode
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * Reset camera to default position
     */
    reset() {
        this.currentMode = this.modes.THIRD_PERSON;
        this.modeIndex = 0;
        this.cinematicTime = 0;
        this.updateCameraMode();

        console.log('📷 Camera reset to default mode');
    }

    /**
     * Handle window resize
     */
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
    }
}
