/**
 * Car Controller Class
 * Handles car physics, movement, and visual representation
 */
class CarController {
    constructor(scene, world) {
        this.scene = scene;
        this.world = world;
        
        // Car properties
        this.mesh = null;
        this.body = null;
        this.wheels = [];
        this.wheelMeshes = [];
        
        // Physics properties
        this.maxSteerVal = 0.5;
        this.maxForce = 1500;
        this.brakeForce = 1000000;
        this.maxSpeed = 100;
        
        // Car state
        this.speed = 0;
        this.engineForce = 0;
        this.vehicleReady = false;
        
        // Starting position
        this.startPosition = { x: 0, y: 2, z: 0 };
        
        this.init();
    }
    
    /**
     * Initialize the car
     */
    init() {
        this.createCarMesh();
        this.createCarPhysics();
        this.setupVehicle();
        
        console.log('🚗 Car initialized successfully!');
    }
    
    /**
     * Create the visual representation of the car
     */
    createCarMesh() {
        // Car body
        const carGeometry = new THREE.BoxGeometry(4, 1.5, 8);
        const carMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xff4444,
            transparent: true,
            opacity: 0.9
        });
        
        this.mesh = new THREE.Mesh(carGeometry, carMaterial);
        this.mesh.position.set(this.startPosition.x, this.startPosition.y, this.startPosition.z);
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        
        // Car details
        this.addCarDetails();
        
        this.scene.add(this.mesh);
    }
    
    /**
     * Add visual details to the car
     */
    addCarDetails() {
        // Windshield
        const windshieldGeometry = new THREE.BoxGeometry(3.5, 1.2, 0.1);
        const windshieldMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.3
        });
        const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
        windshield.position.set(0, 0.3, 2);
        this.mesh.add(windshield);
        
        // Headlights
        const headlightGeometry = new THREE.SphereGeometry(0.3, 8, 8);
        const headlightMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xffffaa,
            emissive: 0x444400
        });
        
        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-1.5, 0, 4);
        this.mesh.add(leftHeadlight);
        
        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(1.5, 0, 4);
        this.mesh.add(rightHeadlight);
        
        // Taillights
        const taillightMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xff0000,
            emissive: 0x440000
        });
        
        const leftTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
        leftTaillight.position.set(-1.5, 0, -4);
        this.mesh.add(leftTaillight);
        
        const rightTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
        rightTaillight.position.set(1.5, 0, -4);
        this.mesh.add(rightTaillight);
        
        // Create wheels
        this.createWheels();
    }
    
    /**
     * Create wheel meshes
     */
    createWheels() {
        const wheelGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.5, 16);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        
        const wheelPositions = [
            { x: -1.7, y: -0.5, z: 2.5 },  // Front left
            { x: 1.7, y: -0.5, z: 2.5 },   // Front right
            { x: -1.7, y: -0.5, z: -2.5 }, // Rear left
            { x: 1.7, y: -0.5, z: -2.5 }   // Rear right
        ];
        
        wheelPositions.forEach((pos, index) => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.rotation.z = Math.PI / 2;
            wheel.position.set(pos.x, pos.y, pos.z);
            wheel.castShadow = true;
            this.mesh.add(wheel);
            this.wheelMeshes.push(wheel);
        });
    }
    
    /**
     * Create car physics body
     */
    createCarPhysics() {
        // Car body
        const carShape = new CANNON.Box(new CANNON.Vec3(2, 0.75, 4));
        this.body = new CANNON.Body({ mass: 500 });
        this.body.addShape(carShape);
        this.body.position.set(this.startPosition.x, this.startPosition.y, this.startPosition.z);
        this.body.material = new CANNON.Material('car');
        
        this.world.add(this.body);
    }
    
    /**
     * Setup vehicle physics
     */
    setupVehicle() {
        // Create vehicle
        this.vehicle = new CANNON.RaycastVehicle({
            chassisBody: this.body,
            indexRightAxis: 0,
            indexUpAxis: 1,
            indexForwardAxis: 2
        });
        
        // Wheel options
        const wheelOptions = {
            radius: 0.8,
            directionLocal: new CANNON.Vec3(0, -1, 0),
            suspensionStiffness: 30,
            suspensionRestLength: 0.3,
            frictionSlip: 5,
            dampingRelaxation: 2.3,
            dampingCompression: 4.4,
            maxSuspensionForce: 100000,
            rollInfluence: 0.01,
            axleLocal: new CANNON.Vec3(-1, 0, 0),
            chassisConnectionPointLocal: new CANNON.Vec3(1, 1, 0),
            maxSuspensionTravel: 0.3,
            customSlidingRotationalSpeed: -30,
            useCustomSlidingRotationalSpeed: true
        };
        
        // Add wheels
        const wheelPositions = [
            new CANNON.Vec3(-1.7, -0.5, 2.5),  // Front left
            new CANNON.Vec3(1.7, -0.5, 2.5),   // Front right
            new CANNON.Vec3(-1.7, -0.5, -2.5), // Rear left
            new CANNON.Vec3(1.7, -0.5, -2.5)   // Rear right
        ];
        
        wheelPositions.forEach((position) => {
            wheelOptions.chassisConnectionPointLocal = position;
            this.vehicle.addWheel(wheelOptions);
        });
        
        this.vehicle.addToWorld(this.world);
        this.vehicleReady = true;
    }
    
    /**
     * Update car physics and visuals
     */
    update(deltaTime, keys) {
        if (!this.vehicleReady) return;
        
        // Handle input
        this.handleInput(keys);
        
        // Update visual mesh position and rotation
        this.mesh.position.copy(this.body.position);
        this.mesh.quaternion.copy(this.body.quaternion);
        
        // Update wheel visuals
        this.updateWheelVisuals();
        
        // Calculate speed
        this.speed = this.body.velocity.length() * 3.6; // Convert to km/h
        
        // Apply engine force and steering
        this.vehicle.applyEngineForce(this.engineForce, 2);
        this.vehicle.applyEngineForce(this.engineForce, 3);
        
        this.vehicle.setSteeringValue(this.steerValue, 0);
        this.vehicle.setSteeringValue(this.steerValue, 1);
    }
    
    /**
     * Handle player input
     */
    handleInput(keys) {
        this.engineForce = 0;
        this.steerValue = 0;
        
        // Forward/Backward
        if (keys.forward) {
            this.engineForce = this.maxForce;
        }
        if (keys.backward) {
            this.engineForce = -this.maxForce * 0.5; // Reverse is slower
        }
        
        // Steering
        if (keys.left) {
            this.steerValue = this.maxSteerVal;
        }
        if (keys.right) {
            this.steerValue = -this.maxSteerVal;
        }
        
        // Apply brakes when no input
        if (!keys.forward && !keys.backward) {
            this.vehicle.setBrake(this.brakeForce * 0.1, 0);
            this.vehicle.setBrake(this.brakeForce * 0.1, 1);
            this.vehicle.setBrake(this.brakeForce * 0.1, 2);
            this.vehicle.setBrake(this.brakeForce * 0.1, 3);
        } else {
            this.vehicle.setBrake(0, 0);
            this.vehicle.setBrake(0, 1);
            this.vehicle.setBrake(0, 2);
            this.vehicle.setBrake(0, 3);
        }
    }
    
    /**
     * Update wheel visual rotation
     */
    updateWheelVisuals() {
        this.vehicle.wheelInfos.forEach((wheel, index) => {
            this.vehicle.updateWheelTransform(index);
            const transform = this.vehicle.wheelInfos[index].worldTransform;
            
            if (this.wheelMeshes[index]) {
                this.wheelMeshes[index].position.copy(transform.position);
                this.wheelMeshes[index].quaternion.copy(transform.quaternion);
            }
        });
    }
    
    /**
     * Get current speed
     */
    getSpeed() {
        return this.speed;
    }
    
    /**
     * Get car position
     */
    getPosition() {
        return this.body.position;
    }
    
    /**
     * Reset car to starting position
     */
    reset() {
        this.body.position.set(this.startPosition.x, this.startPosition.y, this.startPosition.z);
        this.body.quaternion.set(0, 0, 0, 1);
        this.body.velocity.set(0, 0, 0);
        this.body.angularVelocity.set(0, 0, 0);
        
        console.log('🔄 Car reset to starting position');
    }
}
