/**
 * Car Controller Class
 * Handles car physics, movement, and visual representation
 */
class CarController {
    constructor(scene, world) {
        this.scene = scene;
        this.world = world;

        // Car properties
        this.mesh = null;
        this.body = null;
        this.wheelMeshes = [];

        // Car state
        this.speed = 0;

        // Starting position
        this.startPosition = { x: 0, y: 2, z: 0 };

        this.init();
    }

    /**
     * Initialize the car
     */
    init() {
        this.createCarMesh();
        this.createCarPhysics();

        console.log('🚗 Car initialized successfully!');
    }

    /**
     * Create the visual representation of the car
     */
    createCarMesh() {
        // Car body
        const carGeometry = new THREE.BoxGeometry(4, 1.5, 8);
        const carMaterial = new THREE.MeshLambertMaterial({
            color: 0xff4444,
            transparent: true,
            opacity: 0.9
        });

        this.mesh = new THREE.Mesh(carGeometry, carMaterial);
        this.mesh.position.set(this.startPosition.x, this.startPosition.y, this.startPosition.z);
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;

        // Car details
        this.addCarDetails();

        this.scene.add(this.mesh);
    }

    /**
     * Add visual details to the car
     */
    addCarDetails() {
        // Windshield
        const windshieldGeometry = new THREE.BoxGeometry(3.5, 1.2, 0.1);
        const windshieldMaterial = new THREE.MeshLambertMaterial({
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.3
        });
        const windshield = new THREE.Mesh(windshieldGeometry, windshieldMaterial);
        windshield.position.set(0, 0.3, 2);
        this.mesh.add(windshield);

        // Headlights
        const headlightGeometry = new THREE.SphereGeometry(0.3, 8, 8);
        const headlightMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffaa,
            emissive: 0x444400
        });

        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-1.5, 0, 4);
        this.mesh.add(leftHeadlight);

        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(1.5, 0, 4);
        this.mesh.add(rightHeadlight);

        // Taillights
        const taillightMaterial = new THREE.MeshLambertMaterial({
            color: 0xff0000,
            emissive: 0x440000
        });

        const leftTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
        leftTaillight.position.set(-1.5, 0, -4);
        this.mesh.add(leftTaillight);

        const rightTaillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
        rightTaillight.position.set(1.5, 0, -4);
        this.mesh.add(rightTaillight);

        // Create wheels
        this.createWheels();
    }

    /**
     * Create wheel meshes
     */
    createWheels() {
        const wheelGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.5, 16);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

        const wheelPositions = [
            { x: -1.7, y: -0.5, z: 2.5 },  // Front left
            { x: 1.7, y: -0.5, z: 2.5 },   // Front right
            { x: -1.7, y: -0.5, z: -2.5 }, // Rear left
            { x: 1.7, y: -0.5, z: -2.5 }   // Rear right
        ];

        wheelPositions.forEach((pos, index) => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.rotation.z = Math.PI / 2;
            wheel.position.set(pos.x, pos.y, pos.z);
            wheel.castShadow = true;
            this.mesh.add(wheel);
            this.wheelMeshes.push(wheel);
        });
    }

    /**
     * Create car physics body
     */
    createCarPhysics() {
        // Create simple physics body for the car
        this.body = new CarPhysicsBody({
            x: this.startPosition.x,
            y: this.startPosition.y,
            z: this.startPosition.z,
            mass: 500,
            width: 4,
            height: 1.5,
            depth: 8,
            maxSpeed: 30,
            acceleration: 15,
            braking: 25,
            steering: 1.5
        });

        this.world.addBody(this.body);
    }



    /**
     * Update car physics and visuals
     */
    update(deltaTime, keys) {
        // Update physics body
        this.body.update(deltaTime, keys);

        // Update visual mesh position and rotation
        this.mesh.position.copy(this.body.position);
        this.mesh.rotation.y = this.body.rotation.y;

        // Update wheel visuals
        this.updateWheelVisuals();

        // Get speed from physics body
        this.speed = this.body.getSpeed();
    }

    /**
     * Update wheel visual rotation
     */
    updateWheelVisuals() {
        // Simple wheel rotation based on car movement
        const rotationSpeed = this.body.velocity.length() * 0.1;

        this.wheelMeshes.forEach((wheel, index) => {
            // Rotate wheels based on movement
            wheel.rotation.x += rotationSpeed;

            // Steer front wheels
            if (index < 2) { // Front wheels
                wheel.rotation.y = this.body.steerAngle * 0.3;
            }
        });
    }

    /**
     * Get current speed
     */
    getSpeed() {
        return this.speed;
    }

    /**
     * Get car position
     */
    getPosition() {
        return this.body.position;
    }

    /**
     * Reset car to starting position
     */
    reset() {
        this.body.reset(this.startPosition.x, this.startPosition.y, this.startPosition.z);
        console.log('🔄 Car reset to starting position');
    }
}
