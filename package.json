{"name": "car-driving-server", "version": "1.0.0", "description": "Local server for Car Driving Application - serves the game on local network", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "server": "node server.js"}, "keywords": ["car", "driving", "game", "3d", "threejs", "server", "local", "network"], "author": "Car Driving App", "license": "MIT", "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "local"}, "dependencies": {}, "devDependencies": {}}