/**
 * Main Application Entry Point
 * Initializes and coordinates all game systems
 */

// Global game instance
let game = null;

/**
 * Main Game Application Class
 * Coordinates all game systems and manages the overall application state
 */
class CarDrivingApp {
    constructor() {
        this.gameEngine = null;
        this.uiController = null;
        this.isInitialized = false;
        
        // Performance monitoring
        this.fps = 0;
        this.frameCount = 0;
        this.lastTime = performance.now();
        
        console.log('🚗 Car Driving Application starting...');
    }
    
    /**
     * Initialize the application
     */
    async init() {
        try {
            // Check for WebGL support
            if (!this.checkWebGLSupport()) {
                this.showError('WebGL is not supported in your browser. Please use a modern browser.');
                return;
            }
            
            // Initialize game engine
            this.gameEngine = new GameEngine();
            
            // Initialize UI controller
            this.uiController = new UIController(this.gameEngine);
            
            // Wait for assets to load
            await this.loadAssets();
            
            // Create game world
            this.createGameWorld();
            
            // Setup performance monitoring
            this.setupPerformanceMonitoring();
            
            this.isInitialized = true;
            console.log('✅ Car Driving Application initialized successfully!');
            
        } catch (error) {
            console.error('❌ Failed to initialize application:', error);
            this.showError('Failed to initialize the game. Please refresh the page.');
        }
    }
    
    /**
     * Check if WebGL is supported
     */
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return !!gl;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * Load game assets
     */
    async loadAssets() {
        return new Promise((resolve) => {
            // Simulate asset loading time
            // In a real application, you would load 3D models, textures, sounds, etc.
            let loadedAssets = 0;
            const totalAssets = 5;
            
            const loadAsset = () => {
                loadedAssets++;
                if (loadedAssets >= totalAssets) {
                    resolve();
                } else {
                    setTimeout(loadAsset, 200);
                }
            };
            
            setTimeout(loadAsset, 100);
        });
    }
    
    /**
     * Create the game world
     */
    createGameWorld() {
        // Create environment
        this.gameEngine.environment = new Environment(
            this.gameEngine.scene,
            this.gameEngine.world
        );
        
        // Create car
        this.gameEngine.car = new CarController(
            this.gameEngine.scene,
            this.gameEngine.world
        );
        
        // Create camera controller
        this.gameEngine.cameraController = new CameraController(
            this.gameEngine.camera,
            this.gameEngine.car
        );
        
        console.log('🌍 Game world created successfully!');
    }
    
    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        setInterval(() => {
            this.updateFPS();
        }, 1000);
        
        // Add FPS display in development mode
        if (this.isDevelopmentMode()) {
            this.createFPSDisplay();
        }
    }
    
    /**
     * Update FPS counter
     */
    updateFPS() {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        this.fps = Math.round(1000 / deltaTime * this.frameCount);
        this.frameCount = 0;
        this.lastTime = currentTime;
        
        // Update FPS display if it exists
        const fpsDisplay = document.getElementById('fps-display');
        if (fpsDisplay) {
            fpsDisplay.textContent = `FPS: ${this.fps}`;
        }
    }
    
    /**
     * Check if in development mode
     */
    isDevelopmentMode() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.search.includes('debug=true');
    }
    
    /**
     * Create FPS display for development
     */
    createFPSDisplay() {
        const fpsDisplay = document.createElement('div');
        fpsDisplay.id = 'fps-display';
        fpsDisplay.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #00f5ff;
            padding: 0.5rem;
            border-radius: 5px;
            font-family: 'Orbitron', monospace;
            font-size: 0.8rem;
            z-index: 1000;
            border: 1px solid #00f5ff;
        `;
        document.body.appendChild(fpsDisplay);
    }
    
    /**
     * Show error message
     */
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            font-family: 'Orbitron', monospace;
            z-index: 10000;
            max-width: 400px;
        `;
        errorDiv.innerHTML = `
            <h2>Error</h2>
            <p>${message}</p>
            <button onclick="location.reload()" style="
                margin-top: 1rem;
                padding: 0.5rem 1rem;
                background: white;
                color: red;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-family: 'Orbitron', monospace;
            ">Reload Page</button>
        `;
        document.body.appendChild(errorDiv);
    }
    
    /**
     * Handle application errors
     */
    handleError(error) {
        console.error('Application Error:', error);
        
        // Show user-friendly error message
        if (this.uiController) {
            this.uiController.showNotification('An error occurred. Please try again.', 'error');
        }
    }
    
    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.gameEngine) {
            this.gameEngine.stop();
        }
        
        // Remove event listeners
        window.removeEventListener('beforeunload', this.cleanup.bind(this));
        
        console.log('🧹 Application cleaned up');
    }
}

/**
 * Application startup
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Starting Car Driving Application...');
    
    // Create and initialize the application
    game = new CarDrivingApp();
    await game.init();
    
    // Handle page unload
    window.addEventListener('beforeunload', () => {
        if (game) {
            game.cleanup();
        }
    });
    
    // Global error handling
    window.addEventListener('error', (event) => {
        if (game) {
            game.handleError(event.error);
        }
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        if (game) {
            game.handleError(event.reason);
        }
    });
});

/**
 * Add CSS animations for notifications
 */
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .notification button {
        background: none;
        border: none;
        color: #00f5ff;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .notification button:hover {
        background: rgba(0, 245, 255, 0.2);
        border-radius: 50%;
    }
`;
document.head.appendChild(style);

// Export for debugging in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.game = game;
    console.log('🔧 Development mode: Game instance available as window.game');
}
