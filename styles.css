/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    overflow: hidden;
    color: white;
}

/* Canvas */
#game-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.car-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.loading-bar {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin: 2rem auto;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00f5ff, #0080ff);
    border-radius: 3px;
    animation: loading 3s ease-in-out infinite;
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Main Menu */
#main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.menu-content {
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 3rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #00f5ff, #0080ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-car {
    display: inline-block;
    animation: drive 3s ease-in-out infinite;
}

@keyframes drive {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(20px); }
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-btn {
    padding: 1rem 2rem;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    min-width: 200px;
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.menu-btn.primary {
    background: linear-gradient(45deg, #00f5ff, #0080ff);
    border: 2px solid #00f5ff;
    color: white;
}

.menu-btn.primary:hover {
    background: linear-gradient(45deg, #0080ff, #00f5ff);
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.4);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 200;
}

.modal-content {
    background: rgba(0, 0, 0, 0.9);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(0, 245, 255, 0.5);
    backdrop-filter: blur(10px);
    text-align: center;
    max-width: 500px;
    width: 90%;
}

.modal-content h2 {
    margin-bottom: 1.5rem;
    color: #00f5ff;
    font-size: 2rem;
}

/* Controls Grid */
.controls-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 2rem 0;
    text-align: left;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.key {
    background: #00f5ff;
    color: black;
    padding: 0.3rem 0.6rem;
    border-radius: 5px;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Game UI */
#game-ui {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.hud {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.speed-meter {
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem;
    border-radius: 10px;
    border: 2px solid #00f5ff;
    text-align: center;
    min-width: 120px;
}

.meter-label {
    font-size: 0.8rem;
    color: #00f5ff;
    margin-bottom: 0.5rem;
}

.meter-value {
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.meter-unit {
    font-size: 0.8rem;
    color: #ccc;
}

.score-display {
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem 2rem;
    border-radius: 10px;
    border: 2px solid #00f5ff;
    font-size: 1.2rem;
    font-weight: bold;
}

.camera-info {
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem;
    border-radius: 10px;
    border: 2px solid #00f5ff;
    font-size: 0.9rem;
}

/* Mini Controls */
.mini-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    pointer-events: auto;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #00f5ff;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 245, 255, 0.2);
    transform: scale(1.1);
}

/* Instructions */
.instructions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem 2rem;
    border-radius: 10px;
    border: 2px solid #00f5ff;
    text-align: center;
    font-size: 0.9rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2rem;
    }
    
    .hud {
        flex-direction: column;
        gap: 10px;
    }
    
    .controls-grid {
        grid-template-columns: 1fr;
    }
    
    .menu-content {
        padding: 2rem;
        margin: 1rem;
    }
}
