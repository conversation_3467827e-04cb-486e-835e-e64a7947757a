/**
 * Environment Class
 * Creates and manages the game world including roads, buildings, and scenery
 */
class Environment {
    constructor(scene, world) {
        this.scene = scene;
        this.world = world;

        // Environment objects
        this.ground = null;
        this.roads = [];
        this.buildings = [];
        this.trees = [];
        this.streetLights = [];

        this.init();
    }

    /**
     * Initialize the environment
     */
    init() {
        this.createGround();
        this.createRoads();
        this.createBuildings();
        this.createTrees();
        this.createStreetLights();
        this.createSkybox();

        console.log('🌍 Environment created successfully!');
    }

    /**
     * Create the ground plane
     */
    createGround() {
        // Visual ground
        const groundGeometry = new THREE.PlaneGeometry(2000, 2000);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x4a5d23,
            side: THREE.DoubleSide
        });

        this.ground = new THREE.Mesh(groundGeometry, groundMaterial);
        this.ground.rotation.x = -Math.PI / 2;
        this.ground.receiveShadow = true;
        this.scene.add(this.ground);

        // Physics ground (handled by SimplePhysics automatically)
    }

    /**
     * Create road system
     */
    createRoads() {
        // Main road (straight)
        this.createStraightRoad(0, 0.01, 0, 2000, 20);

        // Cross roads
        this.createStraightRoad(0, 0.01, 0, 20, 2000, Math.PI / 2);

        // Road markings
        this.createRoadMarkings();
    }

    /**
     * Create a straight road segment
     */
    createStraightRoad(x, y, z, length, width, rotation = 0) {
        const roadGeometry = new THREE.PlaneGeometry(length, width);
        const roadMaterial = new THREE.MeshLambertMaterial({
            color: 0x333333,
            side: THREE.DoubleSide
        });

        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.position.set(x, y, z);
        road.rotation.x = -Math.PI / 2;
        road.rotation.z = rotation;
        road.receiveShadow = true;

        this.scene.add(road);
        this.roads.push(road);
    }

    /**
     * Create road markings (lane dividers)
     */
    createRoadMarkings() {
        const markingGeometry = new THREE.PlaneGeometry(2, 0.3);
        const markingMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            side: THREE.DoubleSide
        });

        // Main road markings
        for (let i = -1000; i < 1000; i += 20) {
            const marking = new THREE.Mesh(markingGeometry, markingMaterial);
            marking.position.set(i, 0.02, 0);
            marking.rotation.x = -Math.PI / 2;
            this.scene.add(marking);
        }

        // Cross road markings
        for (let i = -1000; i < 1000; i += 20) {
            const marking = new THREE.Mesh(markingGeometry, markingMaterial);
            marking.position.set(0, 0.02, i);
            marking.rotation.x = -Math.PI / 2;
            marking.rotation.z = Math.PI / 2;
            this.scene.add(marking);
        }
    }

    /**
     * Create buildings along the roads
     */
    createBuildings() {
        const buildingConfigs = [
            { x: 50, z: 50, width: 20, height: 30, depth: 15, color: 0x8B4513 },
            { x: -50, z: 50, width: 15, height: 25, depth: 20, color: 0x696969 },
            { x: 80, z: -30, width: 25, height: 35, depth: 18, color: 0x4682B4 },
            { x: -80, z: -30, width: 18, height: 28, depth: 22, color: 0x8B0000 },
            { x: 120, z: 80, width: 30, height: 40, depth: 25, color: 0x2F4F4F },
            { x: -120, z: 80, width: 22, height: 32, depth: 20, color: 0x800080 },
            { x: 150, z: -80, width: 28, height: 38, depth: 24, color: 0x008B8B },
            { x: -150, z: -80, width: 20, height: 30, depth: 18, color: 0x556B2F }
        ];

        buildingConfigs.forEach(config => {
            this.createBuilding(config.x, config.z, config.width, config.height, config.depth, config.color);
        });
    }

    /**
     * Create a single building
     */
    createBuilding(x, z, width, height, depth, color) {
        const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
        const buildingMaterial = new THREE.MeshLambertMaterial({ color: color });

        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        building.position.set(x, height / 2, z);
        building.castShadow = true;
        building.receiveShadow = true;

        this.scene.add(building);
        this.buildings.push(building);

        // Add windows
        this.addWindows(building, width, height, depth);
    }

    /**
     * Add windows to buildings
     */
    addWindows(building, width, height, depth) {
        const windowGeometry = new THREE.PlaneGeometry(2, 2);
        const windowMaterial = new THREE.MeshLambertMaterial({
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.7
        });

        // Front windows
        for (let i = -width/2 + 3; i < width/2; i += 4) {
            for (let j = 2; j < height - 2; j += 4) {
                const window = new THREE.Mesh(windowGeometry, windowMaterial);
                window.position.set(i, j - height/2, depth/2 + 0.1);
                building.add(window);
            }
        }
    }

    /**
     * Create trees for scenery
     */
    createTrees() {
        const treePositions = [
            { x: 30, z: 30 }, { x: -30, z: 30 }, { x: 30, z: -30 }, { x: -30, z: -30 },
            { x: 60, z: 20 }, { x: -60, z: 20 }, { x: 60, z: -20 }, { x: -60, z: -20 },
            { x: 100, z: 60 }, { x: -100, z: 60 }, { x: 100, z: -60 }, { x: -100, z: -60 }
        ];

        treePositions.forEach(pos => {
            this.createTree(pos.x, pos.z);
        });
    }

    /**
     * Create a single tree
     */
    createTree(x, z) {
        // Tree trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.set(x, 4, z);
        trunk.castShadow = true;

        // Tree leaves
        const leavesGeometry = new THREE.SphereGeometry(4, 8, 8);
        const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
        leaves.position.set(x, 10, z);
        leaves.castShadow = true;

        this.scene.add(trunk);
        this.scene.add(leaves);
        this.trees.push({ trunk, leaves });
    }

    /**
     * Create street lights
     */
    createStreetLights() {
        const lightPositions = [
            { x: 15, z: 40 }, { x: -15, z: 40 }, { x: 15, z: -40 }, { x: -15, z: -40 },
            { x: 40, z: 15 }, { x: 40, z: -15 }, { x: -40, z: 15 }, { x: -40, z: -15 }
        ];

        lightPositions.forEach(pos => {
            this.createStreetLight(pos.x, pos.z);
        });
    }

    /**
     * Create a single street light
     */
    createStreetLight(x, z) {
        // Light pole
        const poleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 12);
        const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const pole = new THREE.Mesh(poleGeometry, poleMaterial);
        pole.position.set(x, 6, z);
        pole.castShadow = true;

        // Light fixture
        const fixtureGeometry = new THREE.SphereGeometry(1, 8, 8);
        const fixtureMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffaa,
            emissive: 0x222200
        });
        const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
        fixture.position.set(x, 11, z);

        // Point light
        const pointLight = new THREE.PointLight(0xffffaa, 0.5, 50);
        pointLight.position.set(x, 11, z);
        pointLight.castShadow = true;

        this.scene.add(pole);
        this.scene.add(fixture);
        this.scene.add(pointLight);

        this.streetLights.push({ pole, fixture, light: pointLight });
    }

    /**
     * Create skybox
     */
    createSkybox() {
        const skyGeometry = new THREE.SphereGeometry(1500, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB,
            side: THREE.BackSide
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }

    /**
     * Update environment (for any animated elements)
     */
    update(deltaTime) {
        // Animate street light intensity
        this.streetLights.forEach((streetLight, index) => {
            const time = Date.now() * 0.001;
            const intensity = 0.5 + Math.sin(time + index) * 0.1;
            streetLight.light.intensity = intensity;
        });

        // Gently sway trees
        this.trees.forEach((tree, index) => {
            const time = Date.now() * 0.001;
            const sway = Math.sin(time + index) * 0.02;
            tree.leaves.rotation.z = sway;
            tree.trunk.rotation.z = sway * 0.5;
        });
    }
}
