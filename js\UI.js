/**
 * UI Controller Class
 * Manages all user interface interactions and state
 */
class UIController {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.isLoading = true;
        this.currentScreen = 'loading';
        
        // UI Elements
        this.loadingScreen = document.getElementById('loading-screen');
        this.mainMenu = document.getElementById('main-menu');
        this.gameUI = document.getElementById('game-ui');
        this.controlsModal = document.getElementById('controls-modal');
        this.pauseMenu = document.getElementById('pause-menu');
        
        // Loading progress
        this.loadingProgress = 0;
        this.loadingSteps = [
            'Loading 3D Engine...',
            'Creating Physics World...',
            'Building Environment...',
            'Preparing Car Model...',
            'Setting up Camera...',
            'Ready to Drive!'
        ];
        this.currentLoadingStep = 0;
        
        this.init();
    }
    
    /**
     * Initialize UI controller
     */
    init() {
        this.setupEventListeners();
        this.startLoadingSequence();
        
        console.log('🎮 UI Controller initialized');
    }
    
    /**
     * Setup all event listeners
     */
    setupEventListeners() {
        // Main menu buttons
        document.getElementById('start-game')?.addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('controls-btn')?.addEventListener('click', () => {
            this.showControls();
        });
        
        document.getElementById('settings-btn')?.addEventListener('click', () => {
            this.showSettings();
        });
        
        // Controls modal
        document.getElementById('close-controls')?.addEventListener('click', () => {
            this.hideControls();
        });
        
        // Game UI buttons
        document.getElementById('pause-btn')?.addEventListener('click', () => {
            this.pauseGame();
        });
        
        document.getElementById('camera-btn')?.addEventListener('click', () => {
            if (this.gameEngine.cameraController) {
                this.gameEngine.cameraController.switchCamera();
            }
        });
        
        document.getElementById('reset-btn')?.addEventListener('click', () => {
            if (this.gameEngine.car) {
                this.gameEngine.car.reset();
            }
        });
        
        // Pause menu buttons
        document.getElementById('resume-btn')?.addEventListener('click', () => {
            this.resumeGame();
        });
        
        document.getElementById('restart-btn')?.addEventListener('click', () => {
            this.restartGame();
        });
        
        document.getElementById('main-menu-btn')?.addEventListener('click', () => {
            this.returnToMainMenu();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });
        
        // Click outside modal to close
        this.controlsModal?.addEventListener('click', (event) => {
            if (event.target === this.controlsModal) {
                this.hideControls();
            }
        });
        
        this.pauseMenu?.addEventListener('click', (event) => {
            if (event.target === this.pauseMenu) {
                this.resumeGame();
            }
        });
    }
    
    /**
     * Start the loading sequence
     */
    startLoadingSequence() {
        this.showScreen('loading');
        this.updateLoadingProgress();
    }
    
    /**
     * Update loading progress
     */
    updateLoadingProgress() {
        const progressBar = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('#loading-screen p');
        
        if (this.currentLoadingStep < this.loadingSteps.length) {
            this.loadingProgress = (this.currentLoadingStep / this.loadingSteps.length) * 100;
            
            if (progressBar) {
                progressBar.style.width = this.loadingProgress + '%';
            }
            
            if (loadingText) {
                loadingText.textContent = this.loadingSteps[this.currentLoadingStep];
            }
            
            this.currentLoadingStep++;
            
            // Simulate loading time
            setTimeout(() => {
                this.updateLoadingProgress();
            }, 500);
        } else {
            // Loading complete
            setTimeout(() => {
                this.showMainMenu();
            }, 1000);
        }
    }
    
    /**
     * Show main menu
     */
    showMainMenu() {
        this.showScreen('main-menu');
        this.isLoading = false;
    }
    
    /**
     * Start the game
     */
    startGame() {
        this.showScreen('game');
        this.gameEngine.start();
        
        // Add entrance animation
        this.animateGameStart();
    }
    
    /**
     * Animate game start
     */
    animateGameStart() {
        const gameUI = this.gameUI;
        if (gameUI) {
            gameUI.style.opacity = '0';
            gameUI.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                gameUI.style.transition = 'all 0.5s ease';
                gameUI.style.opacity = '1';
                gameUI.style.transform = 'translateY(0)';
            }, 100);
        }
    }
    
    /**
     * Pause the game
     */
    pauseGame() {
        this.gameEngine.pause();
        this.showModal('pause');
    }
    
    /**
     * Resume the game
     */
    resumeGame() {
        this.gameEngine.resume();
        this.hideModal('pause');
    }
    
    /**
     * Restart the game
     */
    restartGame() {
        this.gameEngine.reset();
        this.gameEngine.resume();
        this.hideModal('pause');
        
        // Show restart notification
        this.showNotification('Game Restarted!', 'success');
    }
    
    /**
     * Return to main menu
     */
    returnToMainMenu() {
        this.gameEngine.stop();
        this.showScreen('main-menu');
        this.hideModal('pause');
    }
    
    /**
     * Show controls modal
     */
    showControls() {
        this.showModal('controls');
    }
    
    /**
     * Hide controls modal
     */
    hideControls() {
        this.hideModal('controls');
    }
    
    /**
     * Show settings (placeholder)
     */
    showSettings() {
        this.showNotification('Settings coming soon!', 'info');
    }
    
    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(event) {
        switch(event.code) {
            case 'Escape':
                if (this.currentScreen === 'game') {
                    if (this.gameEngine.isPaused) {
                        this.resumeGame();
                    } else {
                        this.pauseGame();
                    }
                } else if (this.controlsModal && !this.controlsModal.classList.contains('hidden')) {
                    this.hideControls();
                }
                break;
                
            case 'Enter':
                if (this.currentScreen === 'main-menu') {
                    this.startGame();
                }
                break;
        }
    }
    
    /**
     * Show specific screen
     */
    showScreen(screenName) {
        // Hide all screens
        this.loadingScreen?.classList.add('hidden');
        this.mainMenu?.classList.add('hidden');
        this.gameUI?.classList.add('hidden');
        
        // Show requested screen
        switch(screenName) {
            case 'loading':
                this.loadingScreen?.classList.remove('hidden');
                break;
            case 'main-menu':
                this.mainMenu?.classList.remove('hidden');
                break;
            case 'game':
                this.gameUI?.classList.remove('hidden');
                break;
        }
        
        this.currentScreen = screenName;
    }
    
    /**
     * Show modal
     */
    showModal(modalName) {
        switch(modalName) {
            case 'controls':
                this.controlsModal?.classList.remove('hidden');
                break;
            case 'pause':
                this.pauseMenu?.classList.remove('hidden');
                break;
        }
    }
    
    /**
     * Hide modal
     */
    hideModal(modalName) {
        switch(modalName) {
            case 'controls':
                this.controlsModal?.classList.add('hidden');
                break;
            case 'pause':
                this.pauseMenu?.classList.add('hidden');
                break;
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()">×</button>
        `;
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            border: 2px solid #00f5ff;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 1rem;
            font-family: 'Orbitron', monospace;
            animation: slideIn 0.3s ease;
        `;
        
        // Add to document
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 3000);
    }
    
    /**
     * Update game UI elements
     */
    updateGameUI(gameData) {
        // Update speed
        const speedDisplay = document.getElementById('speed-display');
        if (speedDisplay && gameData.speed !== undefined) {
            speedDisplay.textContent = Math.round(gameData.speed);
        }
        
        // Update score
        const scoreDisplay = document.getElementById('score');
        if (scoreDisplay && gameData.score !== undefined) {
            scoreDisplay.textContent = Math.round(gameData.score);
        }
        
        // Update camera mode
        const cameraModeDisplay = document.getElementById('camera-mode');
        if (cameraModeDisplay && gameData.cameraMode) {
            cameraModeDisplay.textContent = gameData.cameraMode;
        }
    }
    
    /**
     * Get current screen
     */
    getCurrentScreen() {
        return this.currentScreen;
    }
}
