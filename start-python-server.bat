@echo off
title Car Driving Python Server
color 0B

echo.
echo ========================================
echo   🚗 Car Driving Python Server
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python from: https://python.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Python found: 
python --version

echo.
echo 🚀 Starting Car Driving Python Server...
echo.

REM Start the Python server
python python-server.py

echo.
echo 🛑 Server stopped.
pause
